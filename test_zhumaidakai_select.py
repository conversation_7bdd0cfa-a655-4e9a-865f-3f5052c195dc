#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试烟叶主脉打开选择程序
"""

import sys
import os
import time

# 添加coderafactor目录到Python路径
coderafactor_root = "/home/<USER>/xm/code/coderafactor"
if coderafactor_root not in sys.path:
    sys.path.insert(0, coderafactor_root)

def test_import():
    """测试导入"""
    try:
        print("🧪 测试程序导入...")
        import seg_zhumaidakai_select.deploy_seg_zhumaidakai_select as deploy
        print("✅ 程序导入成功")
        return True
    except Exception as e:
        print(f"❌ 程序导入失败: {e}")
        return False

def test_main_function():
    """测试主函数"""
    try:
        print("\n🧪 测试主函数执行...")
        start_time = time.time()
        
        # 导入并运行主函数
        from seg_zhumaidakai_select.deploy_seg_zhumaidakai_select import main
        main()
        
        end_time = time.time()
        print(f"✅ 主函数执行成功，耗时: {end_time - start_time:.2f}秒")
        return True
    except Exception as e:
        print(f"❌ 主函数执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔥🔥🔥 烟叶主脉打开选择程序测试 🔥🔥🔥")
    print("="*60)
    
    # 测试导入
    if not test_import():
        return False
    
    # 测试主函数
    if not test_main_function():
        return False
    
    print("\n🎉 所有测试通过！")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
