#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSON结果对比脚本
对比综合检测系统输出的JSON和原始模块输出的JSON是否一致
"""

import os
import json
import sys
from typing import Dict, List, Any

def load_json(file_path: str) -> Dict[str, Any]:
    """加载JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加载JSON失败 {file_path}: {e}")
        return {}

def compare_shapes(shapes1: List[Dict], shapes2: List[Dict], module_name: str) -> bool:
    """对比shapes数组"""
    if len(shapes1) != len(shapes2):
        print(f"⚠️  {module_name}: shapes数量不同 - 综合系统:{len(shapes1)}, 原始模块:{len(shapes2)}")
        return False
    
    # 按label和points排序进行对比
    def sort_key(shape):
        label = shape.get('label', '')
        points = shape.get('points', [])
        if points and len(points) > 0 and len(points[0]) >= 2:
            return (label, points[0][0], points[0][1])
        return (label, 0, 0)
    
    sorted_shapes1 = sorted(shapes1, key=sort_key)
    sorted_shapes2 = sorted(shapes2, key=sort_key)
    
    differences = 0
    for i, (s1, s2) in enumerate(zip(sorted_shapes1, sorted_shapes2)):
        if s1.get('label') != s2.get('label'):
            print(f"⚠️  {module_name}: 第{i+1}个shape的label不同")
            differences += 1
        
        if s1.get('shape_type') != s2.get('shape_type'):
            print(f"⚠️  {module_name}: 第{i+1}个shape的shape_type不同")
            differences += 1
        
        # 对比points（允许小的浮点数误差）
        points1 = s1.get('points', [])
        points2 = s2.get('points', [])
        if len(points1) != len(points2):
            print(f"⚠️  {module_name}: 第{i+1}个shape的points数量不同")
            differences += 1
        else:
            for j, (p1, p2) in enumerate(zip(points1, points2)):
                if len(p1) >= 2 and len(p2) >= 2:
                    if abs(p1[0] - p2[0]) > 1e-6 or abs(p1[1] - p2[1]) > 1e-6:
                        print(f"⚠️  {module_name}: 第{i+1}个shape的第{j+1}个点坐标不同")
                        differences += 1
                        break
    
    if differences == 0:
        print(f"✅ {module_name}: shapes内容完全一致")
        return True
    else:
        print(f"❌ {module_name}: 发现 {differences} 个差异")
        return False

def compare_module_results(module_name: str, image_name: str) -> bool:
    """对比单个模块的结果"""
    print(f"\n🔍 对比模块: {module_name} - {image_name}")
    
    # 综合系统输出路径
    comprehensive_path = f"/home/<USER>/xm/code/coderebuild/test_data/test_output1/{module_name}/{image_name}.json"
    
    # 原始模块输出路径
    if module_name == "zhumaidakai":
        original_path = f"/home/<USER>/xm/code/coderebuild/seg_det_zhumaidakai/test_output_gpu_pipeline_v2/{image_name}.json"
    elif module_name == "zhumaizoushi":
        original_path = f"/home/<USER>/xm/code/coderebuild/seg_det_zhumaizoushi/test_output_gpu_final/{image_name}.json"
    elif module_name == "zheheng":
        original_path = f"/home/<USER>/xm/code/coderebuild/seg_det_zheheng/test_output_gpu_pipeline_v2/{image_name}.json"
    elif module_name == "zhimai":
        original_path = f"/home/<USER>/xm/code/coderebuild/seg_det_zhimai/test_output_gpu_final/{image_name}.json"
    elif module_name == "zhimaiqing":
        original_path = f"/home/<USER>/xm/code/coderebuild/seg_det_zhimaiqing/test_output_gpu_final/{image_name}.json"
    elif module_name == "lunkuo_canque_fill":
        original_path = f"/home/<USER>/xm/code/coderebuild/seg_det_lunkuo_canque_fill/test_output_gpu_final/{image_name}.json"
    else:
        print(f"❌ 未知模块: {module_name}")
        return False
    
    # 检查文件是否存在
    if not os.path.exists(comprehensive_path):
        print(f"❌ 综合系统输出文件不存在: {comprehensive_path}")
        return False
    
    if not os.path.exists(original_path):
        print(f"❌ 原始模块输出文件不存在: {original_path}")
        return False
    
    # 加载JSON文件
    comprehensive_json = load_json(comprehensive_path)
    original_json = load_json(original_path)
    
    if not comprehensive_json or not original_json:
        return False
    
    # 对比基本信息
    print(f"📊 综合系统: {len(comprehensive_json.get('shapes', []))} 个检测结果")
    print(f"📊 原始模块: {len(original_json.get('shapes', []))} 个检测结果")
    
    # 对比shapes
    comprehensive_shapes = comprehensive_json.get('shapes', [])
    original_shapes = original_json.get('shapes', [])
    
    return compare_shapes(comprehensive_shapes, original_shapes, module_name)

def main():
    """主函数"""
    print("🔍🔍🔍 JSON结果一致性对比 🔍🔍🔍")
    print("="*60)
    
    # 测试图像列表
    image_names = [
        "hn-cz-2023-C2F-C21-3",
        "hn-cz-2023-C2F-C21-4", 
        "hn-cz-2023-C2F-C21-5",
        "hn-cz-2023-C2F-C21-6",
        "hn-cz-2023-C2F-C21-7"
    ]
    
    # 模块列表
    modules = [
        "zhumaidakai",
        "zhumaizoushi", 
        "zheheng",
        "zhimai",
        "zhimaiqing",
        "lunkuo_canque_fill"
    ]
    
    total_comparisons = 0
    successful_comparisons = 0
    
    for module in modules:
        print(f"\n{'='*50}")
        print(f"🔧 检查模块: {module}")
        print(f"{'='*50}")
        
        module_success = 0
        module_total = 0
        
        for image_name in image_names:
            if compare_module_results(module, image_name):
                successful_comparisons += 1
                module_success += 1
            total_comparisons += 1
            module_total += 1
        
        print(f"\n📊 {module} 模块对比结果: {module_success}/{module_total} 一致")
    
    print(f"\n{'='*60}")
    print(f"📊 总体对比结果:")
    print(f"✅ 一致: {successful_comparisons}/{total_comparisons}")
    print(f"❌ 不一致: {total_comparisons - successful_comparisons}/{total_comparisons}")
    
    if successful_comparisons == total_comparisons:
        print(f"🎉 所有JSON结果完全一致！")
        return True
    else:
        print(f"⚠️  存在不一致的结果")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
