'''
                    
    # 烟叶残缺检测 （替换掉）
    run_seg_det_canque()
    # 烟叶主脉打开检测
    run_seg_det_zhumaidakai()
    # 烟叶主脉走势检测
    run_seg_det_zhumaizoushi()
    # 烟叶折痕检测 （重要）
    run_seg_det_zheheng()
    # 烟叶支脉检测 （重要）
    run_seg_det_zhimai()
    # 烟叶支脉青检测 （替换掉）
    run_seg_det_zhimaiqing()

    # 前面五个程序多线程并行检测处理

    # 烟叶轮廓残缺补全 （替换掉）
    run_seg_det_lunkuo_canque_fill()
    bf.sleep(5)
    # 这一部分代码需要优化


    # 烟叶焦点检测（成熟斑、浮青、焦点、气象斑、压油）
    run_test_3030()
    bf.sleep(5)
    # 烟叶横纹检测（不重要）
    run_test_100100()
    bf.sleep(5)
    # 烟叶烤红检测 （替换掉）
    run_test_250250()
    bf.sleep(5)
    # 烟叶潮红检测 （不重要）
    run_test_250250_chaohong()
    bf.sleep(5)
    # 烟叶皱缩检测 （重要）
    run_test_250250_zhousuo()
    bf.sleep(5)

    # 杂色检测模型多线程并行检测处理

    # 主脉打开处理
    run_seg_zhumaidakai_select()
    run_delete_out_labels_tmp()
    run_test_zhumai_zhimai_json_tmp()

    # 这里初步特征汇总

    # 支脉分离处理
    run_zhimai_split_tmp()
    run_delete_out_labels_tmp()
    run_zhimai_rgb_sub_tmp()
    
    # 圆度计算
    run_yuandu_halcon_tmp()
    # 锯齿平滑处理
    run_dealing_images_juchi_tmp()
    # 主要特征分析
    run_import_yanye_user_feature_tmp()
    # 主要特征颜色分析
    run_import_yanye_user_feature_rgb_hist()
    # 烟叶宽度计算
    run_user_feature_tobacoo_width_tmp()
    # 烟叶平滑度计算
    run_user_feature_smoothness()
    # 烟叶RGB直方图处理
    run_split_rgb_hist_tmp()
    # 烟叶相关性分析
    run_correlation_json_generate_tmp()
    # 烟叶特征汇总
    run_sum_yanye_user_feature_tmp()
    # 烟叶颜色多样性分析
    run_color_variety()
    # 烟叶残缺类型分类
    run_canque_type_category_tmp()
    # 烟叶特征排序
    run_user_feature_sort()
    
'''


要求如下：我想把 主脉打开(@/home/<USER>/xm/code/coderafactor/seg_det_zhumaidakai/deploy_seg_det_zhumaidakai_onnx_gpu_final.py),主脉走势(@/home/<USER>/xm/code/coderafactor/seg_det_zhumaizoushi/deploy_seg_det_zhumaizoushi_onnx_gpu_final.py),折痕检测(@/home/<USER>/xm/code/coderafactor/seg_det_zheheng/deploy_seg_det_zheheng_onnx_gpu_final.py),支脉检测(@/home/<USER>/xm/code/coderafactor/seg_det_zhimai/deploy_seg_det_zhimai_onnx_gpu_final.py),支脉青检测(@/home/<USER>/xm/code/coderafactor/seg_det_zhimaiqing/deploy_seg_det_zhimaiqing_onnx_gpu_final.py),轮廓残缺补全(@/home/<USER>/xm/code/coderafactor/seg_det_lunkuo_canque_fill/deploy_seg_lunkuo_canque_fill_onnx_gpu_final.py),焦点浮青等检测(@/home/<USER>/xm/code/coderafactor/seg_det_jiaodian/deploy_seg_det_condition_onnx_gpu_final.py),横纹检测(@/home/<USER>/xm/code/coderafactor/seg_det_hengwen/deploy_seg_det_hengwen_onnx_gpu_final.py),烤红检测(@/home/<USER>/xm/code/coderafactor/seg_det_kaohong/deploy_seg_det_kaohong_onnx_gpu_final.py),挂灰检测(@/home/<USER>/xm/code/coderafactor/seg_det_kaohong/deploy_seg_det_guahui_onnx_gpu_final.py),皱缩检测(@/home/<USER>/xm/code/coderafactor/seg_det_zhousuo/deploy_seg_det_zhousuo_onnx_gpu_final.py),潮红检测(@/home/<USER>/xm/code/coderafactor/seg_det_chaohong/deploy_seg_det_chaohong_onnx_gpu_final.py)这些模块都集成到一个程序comprehensive_det_onnx_gpu.py里面执行GPU流水线并行处理，这些模块都是成熟的流水线并行架构，要求合理地import调用这些模块并安排好当前两块24G显存的GPU资源，使得每个模块互不冲突且高效处理运行。先用"/home/<USER>/xm/code/coderafactor/test_images/"中的前5张图像作为测试，各个模块检测处理完成后进行合并了的json保存在"/home/<USER>/xm/code/coderafactor/test_data/test_output/"中，当然也需要对检测结果进行可视化，在创建一个vis_comprehensive_det_onnx_gpu.py程序来进行可视化，可视化的结果也保存在"/home/<USER>/xm/code/coderafactor/test_data/test_output/"中。 要求必须在编写完成代码之后严格测试运行"source ~/.bashrc && conda activate vllm && python comprehensive_det_onnx_gpu.py"来验证测试结果。先不要编码，禁止编程，那么请你先告诉我按照这个需求做的详细步骤，并将上述详细需求和对应详细的步骤以Markdown的形式以烟叶综合检测详细步骤计划.md保存在@/coderafactor 文件夹 中，我会在对你给我烟叶综合检测详细步骤计划.md之后确认无误之后再要求你编程。

主脉打开(@/home/<USER>/xm/code/coderafactor/seg_det_zhumaidakai/deploy_seg_det_zhumaidakai_onnx_gpu_final.py),主脉走势(@/home/<USER>/xm/code/coderafactor/seg_det_zhumaizoushi/deploy_seg_det_zhumaizoushi_onnx_gpu_final.py),折痕检测(@/home/<USER>/xm/code/coderafactor/seg_det_zheheng/deploy_seg_det_zheheng_onnx_gpu_final.py),支脉检测(@/home/<USER>/xm/code/coderafactor/seg_det_zhimai/deploy_seg_det_zhimai_onnx_gpu_final.py),支脉青检测(@/home/<USER>/xm/code/coderafactor/seg_det_zhimaiqing/deploy_seg_det_zhimaiqing_onnx_gpu_final.py),轮廓残缺补全(@/home/<USER>/xm/code/coderafactor/seg_det_lunkuo_canque_fill/deploy_seg_lunkuo_canque_fill_onnx_gpu_final.py),