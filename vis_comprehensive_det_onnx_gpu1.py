#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
烟叶综合形态检测结果可视化程序
可视化合并后的检测结果，支持多模块结果叠加显示

作者: 系统架构师
日期: 2025-01-30
版本: 1.0.0
"""

import os
import sys
import json
import cv2
import numpy as np
from typing import List, Dict, Tuple, Any
from dataclasses import dataclass

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)


@dataclass
class VisualizationConfig:
    """可视化配置"""
    input_dir: str = "/home/<USER>/xm/code/coderafactor/test_data/vis"
    output_dir: str = "/home/<USER>/xm/code/coderafactor/test_data/test_output1"
    line_thickness: int = 2
    point_radius: int = 3
    font_scale: float = 0.6
    font_thickness: int = 1


class ModuleColorManager:
    """模块颜色管理器"""
    
    def __init__(self):
        # 定义各模块的颜色 (B, G, R)
        self.module_colors = {
            "zhumaidakai": (0, 0, 255),      # 红色
            "zhumaizoushi": (255, 0, 0),     # 蓝色
            "zheheng": (0, 255, 0),          # 绿色
            "zhimai": (0, 255, 255),         # 黄色
            "zhimaiqing": (255, 255, 0),     # 青色
            "lunkuo_canque_fill": (128, 0, 128),  # 紫色
            "canque": (255, 0, 255),         # 品红色
            "beiyong6": (0, 128, 255),       # 橙色
            "canque_fill": (128, 128, 0),    # 橄榄色
            "four_points": (255, 128, 0)     # 深橙色
        }
    
    def get_color(self, module_name: str, label: str = None) -> Tuple[int, int, int]:
        """获取模块或标签的颜色"""
        if label and label in self.module_colors:
            return self.module_colors[label]
        return self.module_colors.get(module_name, (128, 128, 128))  # 默认灰色
    
    def get_all_colors(self) -> Dict[str, Tuple[int, int, int]]:
        """获取所有颜色映射"""
        return self.module_colors.copy()


class ComprehensiveVisualizer:
    """综合检测结果可视化器"""
    
    def __init__(self, config: VisualizationConfig):
        self.config = config
        self.color_manager = ModuleColorManager()
    
    def load_merged_json(self, json_path: str) -> Dict[str, Any]:
        """加载合并的JSON文件"""
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return data
        except Exception as e:
            print(f"❌ 加载JSON文件失败 {json_path}: {e}")
            return {}
    
    def load_image(self, image_path: str) -> np.ndarray:
        """加载图像"""
        try:
            image = cv2.imread(image_path)
            if image is None:
                print(f"❌ 无法加载图像: {image_path}")
                return None
            return image
        except Exception as e:
            print(f"❌ 加载图像失败 {image_path}: {e}")
            return None
    
    def draw_polygon(self, image: np.ndarray, points: List[List[float]], 
                    color: Tuple[int, int, int], label: str = "", 
                    image_width: int = None, image_height: int = None) -> np.ndarray:
        """绘制多边形"""
        if not points or len(points) < 3:
            return image
        
        # 转换相对坐标到绝对坐标
        if image_width is None:
            image_height, image_width = image.shape[:2]
        
        abs_points = []
        for point in points:
            if len(point) >= 2:
                x = int(point[0] * image_width)
                y = int(point[1] * image_height)
                abs_points.append([x, y])
        
        if len(abs_points) < 3:
            return image
        
        # 绘制多边形
        pts = np.array(abs_points, np.int32)
        pts = pts.reshape((-1, 1, 2))
        
        # 填充多边形（半透明）
        overlay = image.copy()
        cv2.fillPoly(overlay, [pts], color)
        cv2.addWeighted(overlay, 0.3, image, 0.7, 0, image)
        
        # 绘制边框
        cv2.polylines(image, [pts], True, color, self.config.line_thickness)
        
        # 添加标签
        if label and len(abs_points) > 0:
            label_pos = (abs_points[0][0], abs_points[0][1] - 10)
            cv2.putText(image, label, label_pos, cv2.FONT_HERSHEY_SIMPLEX, 
                       self.config.font_scale, color, self.config.font_thickness)
        
        return image
    
    def draw_point(self, image: np.ndarray, point: List[float], 
                  color: Tuple[int, int, int], label: str = "",
                  image_width: int = None, image_height: int = None) -> np.ndarray:
        """绘制点"""
        if len(point) < 2:
            return image
        
        # 转换相对坐标到绝对坐标
        if image_width is None:
            image_height, image_width = image.shape[:2]
        
        x = int(point[0] * image_width)
        y = int(point[1] * image_height)
        
        # 绘制点
        cv2.circle(image, (x, y), self.config.point_radius, color, -1)
        
        # 添加标签
        if label:
            label_pos = (x + 5, y - 5)
            cv2.putText(image, label, label_pos, cv2.FONT_HERSHEY_SIMPLEX,
                       self.config.font_scale, color, self.config.font_thickness)
        
        return image
    
    def visualize_shapes(self, image: np.ndarray, shapes: List[Dict], 
                        image_width: int, image_height: int) -> np.ndarray:
        """可视化所有形状"""
        module_counts = {}
        
        for shape in shapes:
            try:
                shape_type = shape.get("shape_type", "")
                label = shape.get("label", "")
                module = shape.get("module", "unknown")
                points = shape.get("points", [])
                
                # 统计各模块检测数量
                if module not in module_counts:
                    module_counts[module] = 0
                module_counts[module] += 1
                
                # 获取颜色
                color = self.color_manager.get_color(module, label)
                
                # 创建显示标签
                display_label = f"{module}_{label}" if label else module
                
                # 根据形状类型绘制
                if shape_type == "polygon" and len(points) >= 3:
                    image = self.draw_polygon(image, points, color, display_label, 
                                            image_width, image_height)
                elif shape_type == "point" and len(points) >= 1:
                    image = self.draw_point(image, points[0], color, display_label,
                                          image_width, image_height)
                elif shape_type == "line" and len(points) >= 2:
                    # 绘制线段
                    for i in range(len(points) - 1):
                        pt1 = points[i]
                        pt2 = points[i + 1]
                        if len(pt1) >= 2 and len(pt2) >= 2:
                            x1 = int(pt1[0] * image_width)
                            y1 = int(pt1[1] * image_height)
                            x2 = int(pt2[0] * image_width)
                            y2 = int(pt2[1] * image_height)
                            cv2.line(image, (x1, y1), (x2, y2), color, self.config.line_thickness)
                
            except Exception as e:
                print(f"⚠️  绘制形状时出错: {e}")
                continue
        
        return image, module_counts
    
    def add_legend(self, image: np.ndarray, module_counts: Dict[str, int]) -> np.ndarray:
        """添加图例"""
        legend_height = 30 + len(module_counts) * 25
        legend_width = 300
        
        # 创建图例区域
        legend = np.zeros((legend_height, legend_width, 3), dtype=np.uint8)
        legend.fill(255)  # 白色背景
        
        # 添加标题
        cv2.putText(legend, "Detection Results", (10, 20), cv2.FONT_HERSHEY_SIMPLEX,
                   0.7, (0, 0, 0), 2)
        
        # 添加各模块信息
        y_offset = 45
        for module, count in module_counts.items():
            color = self.color_manager.get_color(module)
            
            # 绘制颜色块
            cv2.rectangle(legend, (10, y_offset - 10), (30, y_offset + 5), color, -1)
            
            # 添加文本
            text = f"{module}: {count}"
            cv2.putText(legend, text, (40, y_offset), cv2.FONT_HERSHEY_SIMPLEX,
                       0.5, (0, 0, 0), 1)
            
            y_offset += 25
        
        # 将图例添加到图像右上角
        h, w = image.shape[:2]
        if w > legend_width and h > legend_height:
            image[10:10+legend_height, w-legend_width-10:w-10] = legend
        
        return image
    
    def visualize_single_image(self, image_name: str) -> bool:
        """可视化单张图像的检测结果"""
        try:
            # 构建文件路径
            base_name = os.path.splitext(image_name)[0]
            image_path = os.path.join(self.config.input_dir, image_name)
            json_path = os.path.join(self.config.output_dir, f"{base_name}_merged.json")
            output_path = os.path.join(self.config.output_dir, f"{base_name}_visualization.png")
            
            # 检查文件是否存在
            if not os.path.exists(image_path):
                print(f"❌ 图像文件不存在: {image_path}")
                return False
            
            if not os.path.exists(json_path):
                print(f"❌ JSON文件不存在: {json_path}")
                return False
            
            # 加载图像和JSON数据
            image = self.load_image(image_path)
            if image is None:
                return False
            
            json_data = self.load_merged_json(json_path)
            if not json_data:
                return False
            
            # 获取图像尺寸
            image_height = json_data.get("imageHeight", image.shape[0])
            image_width = json_data.get("imageWidth", image.shape[1])
            shapes = json_data.get("shapes", [])
            
            print(f"🎨 可视化 {image_name}: {len(shapes)} 个检测结果")
            
            # 可视化所有形状
            result_image, module_counts = self.visualize_shapes(image, shapes, image_width, image_height)
            
            # 添加图例
            result_image = self.add_legend(result_image, module_counts)
            
            # 添加标题
            title = f"Comprehensive Detection Results: {base_name}"
            cv2.putText(result_image, title, (10, 30), cv2.FONT_HERSHEY_SIMPLEX,
                       1.0, (0, 0, 0), 2)
            
            # 保存结果
            cv2.imwrite(output_path, result_image)
            print(f"✅ 可视化结果保存到: {output_path}")
            
            # 打印统计信息
            total_detections = sum(module_counts.values())
            print(f"📊 检测统计: 总计 {total_detections} 个目标")
            for module, count in sorted(module_counts.items()):
                print(f"   {module}: {count} 个")
            
            return True
            
        except Exception as e:
            print(f"❌ 可视化失败 {image_name}: {e}")
            return False
    
    def visualize_all_images(self) -> bool:
        """可视化所有图像的检测结果"""
        print("🎨 开始可视化综合检测结果")
        print("="*50)
        
        # 获取所有合并的JSON文件
        json_files = [f for f in os.listdir(self.config.output_dir) 
                     if f.endswith('_merged.json')]
        
        if not json_files:
            print("❌ 没有找到合并的JSON文件")
            return False
        
        success_count = 0
        total_count = len(json_files)
        
        for json_file in json_files:
            # 从JSON文件名推导图像文件名
            base_name = json_file.replace('_merged.json', '')
            
            # 查找对应的图像文件
            image_file = None
            for ext in ['.bmp', '.jpg', '.jpeg', '.png']:
                candidate = f"{base_name}{ext}"
                if os.path.exists(os.path.join(self.config.input_dir, candidate)):
                    image_file = candidate
                    break
            
            if image_file:
                if self.visualize_single_image(image_file):
                    success_count += 1
            else:
                print(f"⚠️  未找到对应的图像文件: {base_name}")
        
        print(f"\n📊 可视化完成:")
        print(f"✅ 成功: {success_count}/{total_count} 张图像")
        print(f"📁 输出目录: {self.config.output_dir}")
        
        return success_count > 0


def main():
    """主函数"""
    print("🎨🎨🎨 烟叶综合形态检测结果可视化程序 🎨🎨🎨")
    print("="*60)
    
    # 创建配置
    config = VisualizationConfig()
    
    # 验证目录
    if not os.path.exists(config.input_dir):
        print(f"❌ 输入目录不存在: {config.input_dir}")
        return False
    
    if not os.path.exists(config.output_dir):
        print(f"❌ 输出目录不存在: {config.output_dir}")
        return False
    
    print(f"📁 图像目录: {config.input_dir}")
    print(f"📁 输出目录: {config.output_dir}")
    
    # 创建可视化器
    visualizer = ComprehensiveVisualizer(config)
    
    # 执行可视化
    success = visualizer.visualize_all_images()
    
    if success:
        print(f"\n🎉 可视化程序执行完成！")
    else:
        print(f"\n❌ 可视化程序执行失败！")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
