#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
烟叶综合杂色检测可视化程序
将综合检测结果可视化，支持多模块检测结果的统一显示

作者: 系统架构师
日期: 2025-01-27
版本: 1.0.0

功能特性：
1. 读取综合检测JSON文件
2. 将归一化坐标映射到原图尺寸
3. 为不同模块使用不同颜色编码
4. 绘制检测框、标签和统计信息
5. 生成高质量的可视化结果
"""

import os
import sys
import json
import cv2
import numpy as np
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import traceback

# 添加coderafactor目录到Python路径
coderafactor_root = "/home/<USER>/xm/code/coderafactor"
if coderafactor_root not in sys.path:
    sys.path.insert(0, coderafactor_root)


@dataclass
class ModuleVisualizationConfig:
    """模块可视化配置"""
    name: str
    display_name: str
    color: Tuple[int, int, int]  # BGR格式
    thickness: int = 2
    font_scale: float = 0.6
    enabled: bool = True


class ComprehensiveVisualizationManager:
    """综合检测可视化管理器"""
    
    def __init__(self):
        self.module_configs = {
            'condition': ModuleVisualizationConfig(
                name='condition',
                display_name='焦点浮青',
                color=(0, 255, 255),  # 黄色
                thickness=2,
                font_scale=0.6
            ),
            'hengwen': ModuleVisualizationConfig(
                name='hengwen',
                display_name='横纹',
                color=(255, 0, 0),  # 蓝色
                thickness=2,
                font_scale=0.6
            ),
            'kaohong': ModuleVisualizationConfig(
                name='kaohong',
                display_name='烤红',
                color=(0, 0, 255),  # 红色
                thickness=2,
                font_scale=0.6
            ),
            'guahui': ModuleVisualizationConfig(
                name='guahui',
                display_name='挂灰',
                color=(128, 128, 128),  # 灰色
                thickness=2,
                font_scale=0.6
            ),
            'zhousuo': ModuleVisualizationConfig(
                name='zhousuo',
                display_name='皱缩',
                color=(0, 255, 0),  # 绿色
                thickness=2,
                font_scale=0.6
            ),
            'chaohong': ModuleVisualizationConfig(
                name='chaohong',
                display_name='潮红',
                color=(255, 0, 255),  # 紫色
                thickness=2,
                font_scale=0.6
            )
        }
        
        print("🎨 初始化综合检测可视化管理器")
        print(f"   配置了 {len(self.module_configs)} 个模块的可视化方案")
    
    def visualize_comprehensive_results(self, json_path: str, image_path: str, output_path: str) -> bool:
        """可视化综合检测结果"""
        try:
            print(f"\n🎨 开始可视化综合检测结果")
            print(f"   JSON文件: {os.path.basename(json_path)}")
            print(f"   图像文件: {os.path.basename(image_path)}")
            print(f"   输出文件: {os.path.basename(output_path)}")
            
            # 读取JSON文件
            detection_data = self._load_detection_results(json_path)
            if not detection_data:
                return False
            
            # 加载原始图像
            image = self._load_image(image_path)
            if image is None:
                return False
            
            # 获取图像尺寸
            img_height, img_width = image.shape[:2]
            print(f"   📐 图像尺寸: {img_width}x{img_height}")
            
            # 验证尺寸一致性
            json_width = detection_data.get('image_info', {}).get('width', 0)
            json_height = detection_data.get('image_info', {}).get('height', 0)
            
            if json_width != img_width or json_height != img_height:
                print(f"   ⚠️  尺寸不匹配: JSON({json_width}x{json_height}) vs 图像({img_width}x{img_height})")
                print(f"   🔧 使用图像实际尺寸进行映射")
            
            # 绘制所有模块的检测结果
            result_image = self._draw_all_detections(image, detection_data, img_width, img_height)
            
            # 添加统计信息
            result_image = self._add_statistics_overlay(result_image, detection_data)
            
            # 保存结果
            success = self._save_visualization_result(result_image, output_path)
            
            if success:
                print(f"   ✅ 可视化完成: {output_path}")
                return True
            else:
                print(f"   ❌ 可视化失败")
                return False
                
        except Exception as e:
            print(f"   ❌ 可视化异常: {e}")
            traceback.print_exc()
            return False
    
    def _load_detection_results(self, json_path: str) -> Optional[Dict]:
        """加载检测结果JSON文件"""
        try:
            if not os.path.exists(json_path):
                print(f"   ❌ JSON文件不存在: {json_path}")
                return None
            
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f"   📊 加载JSON成功: {len(data.get('detection_results', {}))} 个模块")
            return data
            
        except Exception as e:
            print(f"   ❌ 加载JSON失败: {e}")
            return None
    
    def _load_image(self, image_path: str) -> Optional[np.ndarray]:
        """加载原始图像"""
        try:
            if not os.path.exists(image_path):
                print(f"   ❌ 图像文件不存在: {image_path}")
                return None
            
            image = cv2.imread(image_path)
            if image is None:
                print(f"   ❌ 无法读取图像: {image_path}")
                return None
            
            print(f"   🖼️  图像加载成功")
            return image
            
        except Exception as e:
            print(f"   ❌ 加载图像失败: {e}")
            return None
    
    def _draw_all_detections(self, image: np.ndarray, detection_data: Dict, 
                           img_width: int, img_height: int) -> np.ndarray:
        """绘制所有模块的检测结果"""
        result_image = image.copy()
        detection_results = detection_data.get('detection_results', {})
        
        total_detections = 0
        
        for module_name, module_data in detection_results.items():
            if module_name not in self.module_configs:
                print(f"   ⚠️  未知模块: {module_name}")
                continue
            
            config = self.module_configs[module_name]
            if not config.enabled:
                continue
            
            shapes = module_data.get('shapes', [])
            if not shapes:
                print(f"   📊 {config.display_name}: 0 个检测结果")
                continue
            
            print(f"   📊 {config.display_name}: {len(shapes)} 个检测结果")
            
            # 绘制该模块的所有检测结果
            result_image = self._draw_module_detections(
                result_image, shapes, config, img_width, img_height
            )
            
            total_detections += len(shapes)
        
        print(f"   🎯 总检测数量: {total_detections}")
        return result_image
    
    def _draw_module_detections(self, image: np.ndarray, shapes: List[Dict], 
                              config: ModuleVisualizationConfig,
                              img_width: int, img_height: int) -> np.ndarray:
        """绘制单个模块的检测结果"""
        for shape in shapes:
            try:
                # 获取坐标点
                points = shape.get('points', [])
                if len(points) < 2:
                    continue
                
                # 将归一化坐标映射到原图尺寸
                x1 = int(points[0][0] * img_width)
                y1 = int(points[0][1] * img_height)
                x2 = int(points[1][0] * img_width)
                y2 = int(points[1][1] * img_height)
                
                # 确保坐标在图像范围内
                x1 = max(0, min(x1, img_width - 1))
                y1 = max(0, min(y1, img_height - 1))
                x2 = max(0, min(x2, img_width - 1))
                y2 = max(0, min(y2, img_height - 1))
                
                # 绘制检测框
                cv2.rectangle(image, (x1, y1), (x2, y2), config.color, config.thickness)
                
                # 绘制标签
                label = shape.get('label', config.name)
                label_text = f"{config.display_name}:{label}"
                
                # 计算标签位置
                label_size = cv2.getTextSize(label_text, cv2.FONT_HERSHEY_SIMPLEX, 
                                           config.font_scale, 1)[0]
                
                # 绘制标签背景
                cv2.rectangle(image, (x1, y1 - label_size[1] - 10), 
                            (x1 + label_size[0] + 5, y1), config.color, -1)
                
                # 绘制标签文字
                cv2.putText(image, label_text, (x1 + 2, y1 - 5),
                          cv2.FONT_HERSHEY_SIMPLEX, config.font_scale, (255, 255, 255), 1)
                
            except Exception as e:
                print(f"     ⚠️  绘制检测框失败: {e}")
                continue
        
        return image
    
    def _add_statistics_overlay(self, image: np.ndarray, detection_data: Dict) -> np.ndarray:
        """添加统计信息覆盖层"""
        try:
            # 获取统计信息
            statistics = detection_data.get('statistics', {})
            total_detections = statistics.get('total_detections', 0)
            
            # 获取各模块检测数量
            detection_results = detection_data.get('detection_results', {})
            module_counts = {}
            
            for module_name, module_data in detection_results.items():
                if module_name in self.module_configs:
                    shapes = module_data.get('shapes', [])
                    module_counts[module_name] = len(shapes)
            
            # 创建统计信息文本
            stats_lines = [
                f"综合检测结果统计",
                f"总检测数量: {total_detections}",
                "=" * 30
            ]
            
            for module_name, count in module_counts.items():
                if count > 0:
                    config = self.module_configs[module_name]
                    stats_lines.append(f"{config.display_name}: {count}")
            
            # 绘制统计信息
            y_offset = 30
            for line in stats_lines:
                cv2.putText(image, line, (10, y_offset), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                cv2.putText(image, line, (10, y_offset), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 1)
                y_offset += 25
            
            return image
            
        except Exception as e:
            print(f"   ⚠️  添加统计信息失败: {e}")
            return image
    
    def _save_visualization_result(self, image: np.ndarray, output_path: str) -> bool:
        """保存可视化结果"""
        try:
            # 创建输出目录
            output_dir = os.path.dirname(output_path)
            os.makedirs(output_dir, exist_ok=True)
            
            # 保存图像
            success = cv2.imwrite(output_path, image)
            
            if success:
                print(f"   💾 保存成功: {output_path}")
                return True
            else:
                print(f"   ❌ 保存失败: {output_path}")
                return False
                
        except Exception as e:
            print(f"   ❌ 保存异常: {e}")
            return False


def main():
    """主函数"""
    try:
        print("🎨🎨🎨 烟叶综合杂色检测可视化程序 🎨🎨🎨")
        print("="*80)
        
        # 配置路径
        input_dir = "/home/<USER>/xm/code/coderafactor/test_data/test_output"
        image_dir = "/home/<USER>/xm/code/coderafactor/test_data/vis"
        output_dir = "/home/<USER>/xm/code/coderafactor/test_data/test_output"
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 初始化可视化管理器
        vis_manager = ComprehensiveVisualizationManager()
        
        # 获取所有综合检测JSON文件
        json_files = [f for f in os.listdir(input_dir) if f.endswith('_comprehensive.json')]
        
        if not json_files:
            print(f"❌ 在输入目录中未找到综合检测JSON文件: {input_dir}")
            return False
        
        print(f"📊 找到 {len(json_files)} 个综合检测JSON文件")
        
        # 处理每个JSON文件
        success_count = 0
        
        for json_file in json_files:
            # 构建文件路径
            json_path = os.path.join(input_dir, json_file)
            
            # 获取对应的图像文件名
            base_name = json_file.replace('_comprehensive.json', '')
            image_file = base_name + '.bmp'  # 假设图像是BMP格式
            image_path = os.path.join(image_dir, image_file)
            
            # 构建输出文件路径
            output_file = base_name + '_comprehensive_vis.jpg'
            output_path = os.path.join(output_dir, output_file)
            
            print(f"\n🎨 处理文件: {json_file}")
            
            # 检查图像文件是否存在
            if not os.path.exists(image_path):
                print(f"   ❌ 对应图像文件不存在: {image_file}")
                continue
            
            # 执行可视化
            success = vis_manager.visualize_comprehensive_results(json_path, image_path, output_path)
            
            if success:
                success_count += 1
                print(f"   ✅ 可视化成功: {output_file}")
            else:
                print(f"   ❌ 可视化失败: {json_file}")
        
        print(f"\n🎉 可视化处理完成！")
        print(f"📊 处理统计:")
        print(f"   - 总文件数: {len(json_files)}")
        print(f"   - 成功处理: {success_count}")
        print(f"   - 成功率: {success_count/len(json_files)*100:.1f}%")
        print(f"📁 输出目录: {output_dir}")
        
        return success_count == len(json_files)
        
    except Exception as e:
        print(f"❌ 主函数执行异常: {e}")
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
