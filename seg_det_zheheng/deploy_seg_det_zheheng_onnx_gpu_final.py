#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
烟叶折痕特征检测GPU流水线并行版本 V2.0
全新的GPU流水线并行架构，确保正确执行

作者: 系统架构师
日期: 2025-07-28
版本: 2.0.0
"""

import os
import sys
import time
import threading
import multiprocessing
import cv2
import numpy as np
import json
import queue
from typing import List, Dict, Optional, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
from PIL import Image
from skimage import morphology

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

try:
    import base_function as bf
    import onnxruntime as ort
    print("🔥🔥🔥 GPU流水线V2.0 - 成功导入所有必要模块 🔥🔥🔥")
except ImportError as e:
    print(f"❌ 错误: 无法导入必要模块 {e}")
    sys.exit(1)


@dataclass
class PipelineData:
    """流水线数据结构"""
    image_path: str
    image_data: Optional[np.ndarray] = None
    preprocessed_data: Optional[np.ndarray] = None
    inference_result: Optional[np.ndarray] = None
    processed_result: Optional[List] = None
    timestamp: float = 0.0
    stage: str = "init"  # init, preprocessed, inferred, postprocessed


class GPUMemoryPool:
    """GPU内存池管理器 - 减少动态内存分配开销"""
    
    def __init__(self, pool_size: int = 8, input_shape: tuple = (1, 3, 512, 1152)):
        self.pool_size = pool_size
        self.input_shape = input_shape
        self.available_buffers = queue.Queue()
        self.used_buffers = set()
        self._lock = threading.Lock()
        
        # 预分配GPU内存缓冲区
        print(f"🔧 初始化GPU内存池 - 池大小: {pool_size}, 输入形状: {input_shape}")
        for _ in range(pool_size):
            buffer = np.zeros(input_shape, dtype=np.float32)
            self.available_buffers.put(buffer)
    
    def get_buffer(self) -> Optional[np.ndarray]:
        """获取可用的GPU内存缓冲区"""
        try:
            buffer = self.available_buffers.get_nowait()
            with self._lock:
                self.used_buffers.add(id(buffer))
            return buffer
        except queue.Empty:
            # 如果池中没有可用缓冲区，创建新的
            print("⚠️  GPU内存池已满，创建临时缓冲区")
            return np.zeros(self.input_shape, dtype=np.float32)
    
    def return_buffer(self, buffer: np.ndarray):
        """归还GPU内存缓冲区"""
        if buffer is not None:
            with self._lock:
                if id(buffer) in self.used_buffers:
                    self.used_buffers.remove(id(buffer))
                    try:
                        self.available_buffers.put_nowait(buffer)
                    except queue.Full:
                        # 如果队列已满，说明有内存泄漏，直接丢弃
                        print("⚠️  GPU内存池队列已满，可能存在内存泄漏")
    
    def get_pool_stats(self) -> Dict[str, int]:
        """获取内存池统计信息"""
        with self._lock:
            return {
                'available': self.available_buffers.qsize(),
                'used': len(self.used_buffers),
                'total': self.pool_size
            }


class AsyncQueue:
    """异步队列 - 支持流水线各阶段的数据传递"""
    
    def __init__(self, maxsize: int = 16):
        self.queue = queue.Queue(maxsize=maxsize)
        self.maxsize = maxsize
        
    def put(self, item, timeout: float = 1.0) -> bool:
        """放入数据项"""
        try:
            self.queue.put(item, timeout=timeout)
            return True
        except queue.Full:
            return False
    
    def get(self, timeout: float = 1.0) -> Optional[Any]:
        """获取数据项"""
        try:
            return self.queue.get(timeout=timeout)
        except queue.Empty:
            return None
    
    def qsize(self) -> int:
        """获取队列大小"""
        return self.queue.qsize()
    
    def empty(self) -> bool:
        """检查队列是否为空"""
        return self.queue.empty()


# 复制原始代码中的后处理函数
def point_sort(points):
    """点排序函数"""
    points = np.array(points)
    center = np.mean(points, axis=0)
    angles = np.arctan2(points[:, 1] - center[1], points[:, 0] - center[0])
    sorted_indices = np.argsort(angles)
    return points[sorted_indices].tolist()


def other_skeleton(bin_image):
    """其他类型的骨架提取"""
    try:
        # 形态学骨架化
        skeleton = morphology.skeletonize(bin_image > 0)
        
        # 查找轮廓
        contours, _ = cv2.findContours(skeleton.astype(np.uint8) * 255, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        edge_points = []
        for contour in contours:
            if len(contour) > 5:  # 过滤太短的轮廓
                # 简化轮廓
                epsilon = 0.02 * cv2.arcLength(contour, False)
                approx = cv2.approxPolyDP(contour, epsilon, False)
                
                # 转换为点列表
                skeleton_points = np.array(approx).reshape(-1, 2)
                # 交换x,y坐标以匹配原始格式
                skeleton_points = skeleton_points[:, [1, 0]]
                edge_points.append(skeleton_points.tolist())
        return edge_points
    except Exception as e:
        print(f"❌ 骨架提取失败: {e}")
        return []


def get_edge_points(binary_mask, threshold=0.5):
    """获取边缘点 - 与CPU版本完全一致"""
    try:
        # 确保是二值图像
        if binary_mask.dtype != np.uint8:
            binary_mask = binary_mask.astype(np.uint8)

        # 使用与CPU版本相同的阈值处理
        _, bin_image = cv2.threshold(binary_mask, threshold * 255, 255, cv2.THRESH_BINARY)

        # 查找轮廓 - 与CPU版本完全相同
        edge_points = []
        if cv2.__version__.startswith('4'):
            edge, hierarchy = cv2.findContours(bin_image, cv2.RETR_CCOMP, cv2.CHAIN_APPROX_SIMPLE)
            edge_points = [np.reshape(e, (-1, 2)).tolist() for e in edge]
        else:
            _, edge, hierarchy = cv2.findContours(bin_image, cv2.RETR_CCOMP, cv2.CHAIN_APPROX_SIMPLE)
            edge_points = [np.reshape(e, (-1, 2)).tolist() for e in edge]

        # 使用与CPU版本完全相同的过滤逻辑
        edges = []
        for i in edge_points:
            if len(i) > 0:  # 与CPU版本保持一致
                edges.append(i)

        return edges
    except Exception as e:
        print(f"❌ 获取边缘点失败: {e}")
        return []


class GPUZhehengModelManager:
    """GPU折痕检测模型管理器 - 线程安全版本"""
    
    # 类级别的锁，用于线程安全的模型加载
    _model_load_lock = threading.Lock()
    
    def __init__(self, model_path=None, thread_id=None):
        """
        初始化GPU模型管理器
        Args:
            model_path: 模型文件路径
            thread_id: 线程ID，用于标识和调试
        """
        self.model_path = model_path
        self.thread_id = thread_id or threading.current_thread().ident
        self.session = None
        self.input_name = None
        self.output_name = None
        self.target_height = 512
        self.target_width = 1152
        self._initialized = False
        
        print(f"🔶 [线程{self.thread_id}] 创建折痕检测GPU模型管理器")
        
        if model_path:
            self._load_model()
            self._initialized = True

    def _load_model(self):
        """加载GPU ONNX模型 - 线程安全版本"""
        with self._model_load_lock:
            print(f"🔶 [线程{self.thread_id}] 开始加载折痕检测模型...")

            try:
                if not os.path.exists(self.model_path):
                    raise FileNotFoundError(f"ONNX模型文件不存在: {self.model_path}")

                # GPU优先策略 - 这是GPU版本，应该优先使用GPU
                providers = []

                # 检查是否强制使用CPU（用于调试或特殊情况）
                force_cpu = os.environ.get('ZHEHENG_FORCE_CPU', 'false').lower() == 'true'

                # 调试信息
                available_providers = ort.get_available_providers()
                print(f"🔍 [线程{self.thread_id}] 可用的执行提供程序: {available_providers}")
                print(f"🔍 [线程{self.thread_id}] 强制使用CPU: {force_cpu}")
                print(f"🔍 [线程{self.thread_id}] CUDA可用: {'CUDAExecutionProvider' in available_providers}")

                if not force_cpu and 'CUDAExecutionProvider' in available_providers:
                    # GPU版本优先使用GPU
                    cuda_options = {
                        'device_id': 0,
                        'arena_extend_strategy': 'kNextPowerOfTwo',
                        'gpu_mem_limit': 2 * 1024 * 1024 * 1024,  # 2GB，增加GPU内存限制
                        'cudnn_conv_algo_search': 'EXHAUSTIVE',  # 优化卷积算法
                        'do_copy_in_default_stream': True,  # 优化内存拷贝
                    }
                    providers.append(('CUDAExecutionProvider', cuda_options))
                    print(f"🔶 [线程{self.thread_id}] ✅ 使用CUDA GPU加速")
                    use_gpu = True
                else:
                    if force_cpu:
                        print(f"🔶 [线程{self.thread_id}] ⚠️ 强制使用CPU（调试模式）")
                    else:
                        print(f"🔶 [线程{self.thread_id}] ❌ CUDA不可用，回退到CPU")
                    use_gpu = False

                providers.append('CPUExecutionProvider')

                # 创建推理会话 - 针对GPU/CPU的优化配置
                sess_options = ort.SessionOptions()
                sess_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL

                if use_gpu:
                    # GPU优化配置
                    sess_options.execution_mode = ort.ExecutionMode.ORT_SEQUENTIAL
                    sess_options.inter_op_num_threads = 1  # GPU推理使用单线程
                    sess_options.intra_op_num_threads = 1
                    # GPU特有的内存优化
                    sess_options.enable_mem_pattern = True
                    sess_options.enable_cpu_mem_arena = False  # GPU模式下禁用CPU内存池
                    print(f"🔶 [线程{self.thread_id}] GPU推理配置：单线程模式")
                else:
                    # CPU优化配置
                    sess_options.execution_mode = ort.ExecutionMode.ORT_PARALLEL
                    sess_options.inter_op_num_threads = 4  # 利用多核CPU
                    sess_options.intra_op_num_threads = 4  # 利用多核CPU
                    # CPU特有的内存优化
                    sess_options.enable_mem_pattern = True
                    sess_options.enable_cpu_mem_arena = True
                    print(f"🔶 [线程{self.thread_id}] CPU推理配置：多线程模式")

                # 创建推理会话 - 无论GPU还是CPU都需要创建
                self.session = ort.InferenceSession(
                    self.model_path,
                    sess_options=sess_options,
                    providers=providers
                )

                # 获取输入输出信息
                self.input_name = self.session.get_inputs()[0].name
                self.output_name = self.session.get_outputs()[0].name

                # 获取输入形状信息
                input_shape = self.session.get_inputs()[0].shape
                print(f"🔶 [线程{self.thread_id}] 模型输入形状: {input_shape}")

                # 获取执行提供程序信息
                providers_info = self.session.get_providers()
                print(f"✅ [线程{self.thread_id}] 折痕检测模型加载成功: {self.model_path}")
                print(f"   执行提供程序: {providers_info[0]}")

                # GPU预热 - 如果使用GPU，进行预热以获得最佳性能
                if use_gpu and providers_info[0] == 'CUDAExecutionProvider':
                    self._warmup_gpu()

            except Exception as e:
                print(f"❌ 模型加载失败: {e}")
                raise

    def _warmup_gpu(self):
        """GPU预热 - 执行一次虚拟推理以预热GPU"""
        try:
            print(f"🔥 [线程{self.thread_id}] GPU预热中...")
            # 创建虚拟输入数据
            dummy_input = np.random.randn(1, 3, 512, 1152).astype(np.float32)

            # 执行预热推理
            _ = self.session.run([self.output_name], {self.input_name: dummy_input})
            print(f"✅ [线程{self.thread_id}] GPU预热完成")

        except Exception as e:
            print(f"⚠️  [线程{self.thread_id}] GPU预热失败: {e}")
            # 预热失败不影响正常使用

    def preprocess_image(self, img: np.ndarray) -> Optional[np.ndarray]:
        """
        预处理图像 - 与CPU版本完全一致

        Args:
            img: 输入图像 (H, W, C)

        Returns:
            预处理后的图像 (1, C, H, W)
        """
        try:
            if img is None:
                return None

            # 转换为RGB
            if len(img.shape) == 3 and img.shape[2] == 3:
                img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            else:
                img_rgb = img

            # 转换为float32并归一化到[0,1]
            img_float = img_rgb.astype(np.float32) / 255.0

            # ImageNet标准化
            mean = np.array([0.485, 0.456, 0.406], dtype=np.float32)
            std = np.array([0.229, 0.224, 0.225], dtype=np.float32)

            img_normalized = (img_float - mean) / std

            # 转换为CHW格式
            img_chw = np.transpose(img_normalized, (2, 0, 1))

            # 添加batch维度
            img_batch = np.expand_dims(img_chw, axis=0)

            return img_batch

        except Exception as e:
            print(f"❌ [线程{self.thread_id}] 预处理失败: {e}")
            return None

    def predict(self, input_data: np.ndarray) -> Optional[np.ndarray]:
        """
        执行推理

        Args:
            input_data: 预处理后的输入数据

        Returns:
            推理结果
        """
        try:
            if self.session is None:
                print(f"❌ [线程{self.thread_id}] 模型未初始化")
                return None

            if input_data is None:
                return None

            # 执行推理
            result = self.session.run([self.output_name], {self.input_name: input_data})
            return result[0]

        except Exception as e:
            print(f"❌ [线程{self.thread_id}] 推理失败: {e}")
            return None

    def postprocess_output(self, output: np.ndarray, threshold: float = 0.5) -> Optional[np.ndarray]:
        """
        后处理输出 - 与CPU版本完全一致

        Args:
            output: 模型输出
            threshold: 二值化阈值

        Returns:
            二值化mask
        """
        try:
            if output is None:
                return None

            # 与CPU版本完全一致的处理逻辑
            # 移除batch维度
            output = np.squeeze(output, axis=0)

            # 移除通道维度（如果只有一个通道）
            if output.shape[0] == 1:
                output = np.squeeze(output, axis=0)

            # 使用与CPU版本相同的阈值处理
            # 不使用sigmoid激活，直接使用cv2.threshold
            _, binary_mask = cv2.threshold(output, threshold, 255, cv2.THRESH_BINARY)
            binary_mask = binary_mask.astype(np.uint8)

            return binary_mask

        except Exception as e:
            print(f"❌ [线程{self.thread_id}] 后处理失败: {e}")
            return None


class PipelineZhehengProcessor:
    """流水线折痕检测处理器 - 真正的并行流水线架构"""

    def __init__(self, model_manager: 'GPUZhehengModelManager',
                 preprocess_workers: int = 2, postprocess_workers: int = 2,
                 queue_size: int = 16):
        self.model_manager = model_manager
        self.preprocess_workers = preprocess_workers
        self.postprocess_workers = postprocess_workers

        # 初始化队列系统
        self.preprocess_queue = AsyncQueue(queue_size)
        self.inference_queue = AsyncQueue(queue_size)
        self.postprocess_queue = AsyncQueue(queue_size)
        self.result_queue = AsyncQueue(queue_size)

        # 初始化GPU内存池
        self.memory_pool = GPUMemoryPool(pool_size=queue_size)

        # 控制标志
        self._stop_event = threading.Event()
        self._threads = []

        # 统计信息
        self.stats = {
            'processed_count': 0,
            'preprocess_time': 0.0,
            'inference_time': 0.0,
            'postprocess_time': 0.0,
            'total_time': 0.0
        }

        print(f"🚀 初始化流水线处理器 - 预处理工作线程: {preprocess_workers}, 后处理工作线程: {postprocess_workers}")

    def _preprocess_worker(self, worker_id: int):
        """预处理工作线程"""
        print(f"🔵 启动预处理工作线程 {worker_id}")

        while not self._stop_event.is_set():
            # 从预处理队列获取任务
            item = self.preprocess_queue.get(timeout=0.1)
            if item is None:
                continue

            try:
                start_time = time.time()

                # 读取和预处理图像
                image = cv2.imread(item.image_path)
                if image is None:
                    print(f"⚠️  [预处理{worker_id}] 无法读取图像: {item.image_path}")
                    continue

                # 调整大小
                image_resized = bf.cv2_resize(image, 1152, 512)

                # 预处理
                preprocessed = self.model_manager.preprocess_image(image_resized)
                if preprocessed is None:
                    print(f"⚠️  [预处理{worker_id}] 预处理失败: {item.image_path}")
                    continue

                # 获取GPU内存缓冲区
                gpu_buffer = self.memory_pool.get_buffer()
                if gpu_buffer is not None:
                    np.copyto(gpu_buffer, preprocessed)
                    item.preprocessed_data = gpu_buffer
                else:
                    item.preprocessed_data = preprocessed

                item.stage = "preprocessed"
                item.timestamp = time.time()

                # 放入推理队列
                if not self.inference_queue.put(item, timeout=1.0):
                    print(f"⚠️  [预处理{worker_id}] 推理队列已满，丢弃任务")
                    if gpu_buffer is not None:
                        self.memory_pool.return_buffer(gpu_buffer)
                    continue

                preprocess_time = time.time() - start_time
                self.stats['preprocess_time'] += preprocess_time

            except Exception as e:
                print(f"❌ [预处理{worker_id}] 处理异常: {e}")
                continue

        print(f"🔵 预处理工作线程 {worker_id} 已停止")

    def _inference_worker(self):
        """推理工作线程 - 单线程GPU推理"""
        print(f"🔶 启动GPU推理工作线程")

        while not self._stop_event.is_set():
            # 从推理队列获取任务
            item = self.inference_queue.get(timeout=0.1)
            if item is None:
                continue

            try:
                start_time = time.time()

                # GPU推理
                result = self.model_manager.predict(item.preprocessed_data)
                if result is None:
                    print(f"⚠️  [推理] GPU推理失败: {item.image_path}")
                    # 归还GPU内存
                    if item.preprocessed_data is not None:
                        self.memory_pool.return_buffer(item.preprocessed_data)
                    continue

                item.inference_result = result
                item.stage = "inferred"

                # 归还GPU内存缓冲区
                if item.preprocessed_data is not None:
                    self.memory_pool.return_buffer(item.preprocessed_data)
                    item.preprocessed_data = None

                # 放入后处理队列
                if not self.postprocess_queue.put(item, timeout=1.0):
                    print(f"⚠️  [推理] 后处理队列已满，丢弃任务")
                    continue

                inference_time = time.time() - start_time
                self.stats['inference_time'] += inference_time

            except Exception as e:
                print(f"❌ [推理] 处理异常: {e}")
                # 归还GPU内存
                if item.preprocessed_data is not None:
                    self.memory_pool.return_buffer(item.preprocessed_data)
                continue

        print(f"🔶 GPU推理工作线程已停止")

    def _postprocess_worker(self, worker_id: int, json_dir: str, threshold: float):
        """后处理工作线程"""
        print(f"🟢 启动后处理工作线程 {worker_id}")

        while not self._stop_event.is_set():
            # 从后处理队列获取任务
            item = self.postprocess_queue.get(timeout=0.1)
            if item is None:
                continue

            try:
                start_time = time.time()

                # 后处理
                binary_mask = self.model_manager.postprocess_output(item.inference_result, threshold)
                if binary_mask is None:
                    print(f"⚠️  [后处理{worker_id}] 后处理失败: {item.image_path}")
                    continue

                # 获取边缘点 - 使用与CPU版本相同的函数
                edges = get_edge_points(binary_mask, threshold)

                # 生成shapes - 与CPU版本完全一致的逻辑
                shapes = []
                for edge in edges:
                    if len(edge) > 2:  # 至少需要3个点才能形成有效的linestrip - 与CPU版本一致
                        # 转换为相对坐标 - 与CPU版本完全一致
                        relative_points = []
                        for point in edge:
                            x, y = point
                            # 转换为相对坐标 (0-1范围) - 使用固定尺寸与CPU版本一致
                            rel_x = x / 1152  # target_width
                            rel_y = y / 512   # target_height
                            relative_points.append([rel_x, rel_y])

                        # 创建shape - 与CPU版本完全相同的格式
                        shape = {
                            "label": "zheheng",
                            "points": relative_points,
                            "group_id": None,
                            "shape_type": "linestrip",
                            "flags": {}
                        }
                        shapes.append(shape)

                # 保存JSON - 使用与CPU版本相同的方式
                if shapes:
                    json_name = bf.get_file_name(bf.rename_add_post(item.image_path, post="json"))
                    json_path = bf.pathjoin(json_dir, json_name)

                    # 使用线程安全的JSON保存 - 与CPU版本一致
                    bf.labelme_json_append_labels_by_shapename_withlock(
                        json_path, "shapes", shapes, ["zheheng"]
                    )

                # 与CPU版本保持一致的输出格式
                print(f"✅ [后处理{worker_id}] 处理完成: {bf.get_file_name(item.image_path)} - 检测到 {len(shapes)} 个zheheng")

                item.processed_result = shapes
                item.stage = "postprocessed"

                # 放入结果队列
                self.result_queue.put(item, timeout=1.0)

                postprocess_time = time.time() - start_time
                self.stats['postprocess_time'] += postprocess_time
                self.stats['processed_count'] += 1

            except Exception as e:
                print(f"❌ [后处理{worker_id}] 处理异常: {e}")
                continue

        print(f"🟢 后处理工作线程 {worker_id} 已停止")

    def start_pipeline(self, json_dir: str, threshold: float = 0.5):
        """启动流水线处理"""
        print("🚀 启动流水线处理...")

        # 启动预处理工作线程
        for i in range(self.preprocess_workers):
            thread = threading.Thread(target=self._preprocess_worker, args=(i,))
            thread.daemon = True
            thread.start()
            self._threads.append(thread)

        # 启动推理工作线程
        thread = threading.Thread(target=self._inference_worker)
        thread.daemon = True
        thread.start()
        self._threads.append(thread)

        # 启动后处理工作线程
        for i in range(self.postprocess_workers):
            thread = threading.Thread(target=self._postprocess_worker,
                                    args=(i, json_dir, threshold))
            thread.daemon = True
            thread.start()
            self._threads.append(thread)

        print(f"✅ 流水线已启动 - 总线程数: {len(self._threads)}")

    def stop_pipeline(self):
        """停止流水线处理"""
        print("🛑 停止流水线处理...")
        self._stop_event.set()

        # 等待所有线程结束
        for thread in self._threads:
            thread.join(timeout=2.0)

        print("✅ 流水线已停止")

    def process_images_pipeline(self, image_paths: List[str], json_dir: str,
                              threshold: float = 0.5) -> int:
        """使用流水线处理图像列表"""
        if not image_paths:
            return 0

        total_start_time = time.time()

        # 启动流水线
        self.start_pipeline(json_dir, threshold)

        try:
            # 将所有图像路径放入预处理队列
            print(f"📥 将 {len(image_paths)} 张图像放入处理队列...")
            for image_path in image_paths:
                item = PipelineData(image_path=image_path, timestamp=time.time())

                # 等待队列有空间
                while not self.preprocess_queue.put(item, timeout=0.1):
                    if self._stop_event.is_set():
                        break
                    time.sleep(0.01)

            # 等待所有任务完成
            print("⏳ 等待所有任务完成...")
            processed_count = 0
            total_detections = 0

            # 监控处理进度
            last_progress_time = time.time()
            while processed_count < len(image_paths):
                result_item = self.result_queue.get(timeout=1.0)
                if result_item is not None:
                    processed_count += 1
                    if result_item.processed_result:
                        total_detections += len(result_item.processed_result)

                    # 显示进度（每5秒或每10%显示一次）
                    current_time = time.time()
                    progress = (processed_count / len(image_paths)) * 100
                    if (current_time - last_progress_time > 5.0) or (processed_count % max(1, len(image_paths) // 10) == 0):
                        # 获取队列状态
                        preprocess_size = self.preprocess_queue.qsize()
                        inference_size = self.inference_queue.qsize()
                        postprocess_size = self.postprocess_queue.qsize()
                        memory_stats = self.memory_pool.get_pool_stats()

                        print(f"📊 进度: {processed_count}/{len(image_paths)} ({progress:.1f}%) | "
                              f"队列: 预处理{preprocess_size} 推理{inference_size} 后处理{postprocess_size} | "
                              f"内存池: {memory_stats['available']}/{memory_stats['total']}")
                        last_progress_time = current_time

                # 检查是否超时
                if time.time() - total_start_time > 300:  # 5分钟超时
                    print("⚠️  处理超时，强制停止")
                    break

            total_time = time.time() - total_start_time
            self.stats['total_time'] = total_time

            # 输出性能统计
            self._print_performance_stats(len(image_paths), total_detections, total_time)

            return total_detections

        finally:
            # 停止流水线
            self.stop_pipeline()

    def _print_performance_stats(self, total_images: int, total_detections: int, total_time: float):
        """打印性能统计信息"""
        print(f"\n📊 流水线性能统计:")
        print(f"   总图像数量: {total_images}")
        print(f"   总检测数量: {total_detections}")
        print(f"   总处理时间: {total_time:.2f}秒")
        print(f"   平均每张图像: {total_time/total_images:.2f}秒")
        print(f"   处理速度: {total_images/total_time:.2f} 张/秒")

        if self.stats['processed_count'] > 0:
            avg_preprocess = self.stats['preprocess_time'] / self.stats['processed_count']
            avg_inference = self.stats['inference_time'] / self.stats['processed_count']
            avg_postprocess = self.stats['postprocess_time'] / self.stats['processed_count']

            print(f"   平均预处理时间: {avg_preprocess:.3f}秒")
            print(f"   平均推理时间: {avg_inference:.3f}秒")
            print(f"   平均后处理时间: {avg_postprocess:.3f}秒")

            # 计算并行效率
            sequential_time = avg_preprocess + avg_inference + avg_postprocess
            parallel_efficiency = (sequential_time / (total_time/total_images)) * 100
            print(f"   并行效率: {parallel_efficiency:.1f}%")


def main():
    """主函数"""
    try:
        print("🔥🔥🔥 这是GPU流水线并行版本V2.0 - 全新架构 🔥🔥🔥")
        print("🔥🔥🔥 执行main()函数 - 确认执行正确版本 🔥🔥🔥")
        print("✅ 成功导入所有必要模块")
        print("🧪 烟叶折痕特征检测GPU流水线并行版本V2.0")
        print("="*60)

        # 配置路径
        current_dir = os.path.dirname(os.path.abspath(__file__))

        # 输入路径配置 - 可以根据需要修改
        input_image_dir = os.path.join(current_dir, 'test_images')
        output_json_dir = os.path.join(current_dir, 'test_output_gpu_pipeline_v2')

        # 路径标准化
        input_image_dir = os.path.abspath(input_image_dir)
        output_json_dir = os.path.abspath(output_json_dir)

        # 设置默认模型路径
        model_path = os.path.join(current_dir, "zheheng.onnx")

        # 设置输出目录
        os.makedirs(output_json_dir, exist_ok=True)

        # 验证输入目录
        if not os.path.exists(input_image_dir):
            print(f"❌ 输入目录不存在: {input_image_dir}")
            return False

        # 获取图像文件列表
        image_files = []
        for ext in ['.bmp', '.png', '.jpg', '.jpeg']:
            image_files.extend([f for f in os.listdir(input_image_dir) if f.lower().endswith(ext)])

        if not image_files:
            print(f"❌ 在目录中未找到图像文件: {input_image_dir}")
            return False

        print(f"📊 找到 {len(image_files)} 个图像文件")

        # 初始化GPU模型管理器 - 线程安全版本
        current_thread_id = threading.current_thread().ident
        print(f"🔶 [线程{current_thread_id}] 初始化折痕检测组件...")

        model_manager = GPUZhehengModelManager(model_path, thread_id=current_thread_id)

        # 使用流水线并行处理器
        pipeline_processor = PipelineZhehengProcessor(
            model_manager,
            preprocess_workers=2,  # 2个预处理线程
            postprocess_workers=2  # 2个后处理线程
        )

        # 构建图像路径列表
        image_paths = [os.path.join(input_image_dir, image_file) for image_file in image_files]

        print(f"\n🚀 开始流水线并行处理...")

        # 流水线并行处理所有图像
        total_detections = pipeline_processor.process_images_pipeline(
            image_paths, output_json_dir, threshold=0.5
        )

        print(f"\n🎉 折痕检测GPU流水线并行处理完成！")
        print(f"📊 总检测数量: {total_detections}")
        print(f"📊 平均每张图像检测数: {total_detections/len(image_files):.1f}")
        print(f"🚀 流水线并行架构 - 预处理/推理/后处理并行执行")

        return True

    except Exception as e:
        print(f"❌ 主函数执行异常: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    main()
