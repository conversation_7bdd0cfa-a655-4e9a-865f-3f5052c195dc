#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
烟叶综合形态检测系统 - GPU流水线并行版本
集成6个检测模块：主脉打开、主脉走势、折痕、支脉、支脉青、轮廓残缺补全

作者: 系统架构师
日期: 2025-01-30
版本: 1.0.0
"""

import os
import sys
import time
import json
import threading
import multiprocessing
from typing import List, Dict, Optional, Any, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
import logging

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
# 当前文件在 coderebuild 目录下，直接使用当前目录作为项目根目录
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

try:
    import torch
    import cv2
    import numpy as np
    print("✅ 成功导入基础模块")
except ImportError as e:
    print(f"❌ 错误: 无法导入基础模块 {e}")
    sys.exit(1)


@dataclass
class DetectionConfig:
    """检测配置"""
    input_dir: str = "/home/<USER>/xm/code/coderebuild/test_data/vis"
    output_dir: str = "/home/<USER>/xm/code/coderebuild/test_data/test_output1"
    gpu_primary: int = 1  # 主GPU
    gpu_secondary: int = 0  # 备用GPU
    threshold: float = 0.5
    max_workers: int = 6  # 最大并行模块数
    enable_visualization: bool = True


@dataclass
class ModuleInfo:
    """模块信息"""
    name: str
    module_path: str
    label_name: str
    memory_requirement: str  # 'high', 'medium', 'low'
    gpu_preference: int  # 优先GPU
    color: Tuple[int, int, int]  # 可视化颜色 (B, G, R)


class GPUResourceManager:
    """GPU资源智能分配管理器"""
    
    def __init__(self, primary_gpu: int = 1, secondary_gpu: int = 0):
        self.primary_gpu = primary_gpu
        self.secondary_gpu = secondary_gpu
        self.gpu_memory_usage = {primary_gpu: 0.0, secondary_gpu: 0.0}
        self.gpu_lock = threading.Lock()
        self._check_gpu_availability()
    
    def _check_gpu_availability(self):
        """检查GPU可用性"""
        try:
            if torch.cuda.is_available():
                gpu_count = torch.cuda.device_count()
                print(f"🔧 检测到 {gpu_count} 个GPU设备")
                
                for i in range(gpu_count):
                    gpu_name = torch.cuda.get_device_name(i)
                    memory_total = torch.cuda.get_device_properties(i).total_memory / 1024**3
                    print(f"   GPU{i}: {gpu_name} ({memory_total:.1f}GB)")
                
                if self.primary_gpu >= gpu_count:
                    print(f"⚠️  主GPU{self.primary_gpu}不可用，使用GPU0")
                    self.primary_gpu = 0
                
                if self.secondary_gpu >= gpu_count:
                    print(f"⚠️  备用GPU{self.secondary_gpu}不可用，使用GPU0")
                    self.secondary_gpu = 0
            else:
                print("❌ 未检测到CUDA GPU，将使用CPU")
                self.primary_gpu = -1
                self.secondary_gpu = -1
        except Exception as e:
            print(f"❌ GPU检查失败: {e}")
            self.primary_gpu = -1
            self.secondary_gpu = -1
    
    def allocate_gpu(self, module_info: ModuleInfo) -> int:
        """为模块分配GPU"""
        with self.gpu_lock:
            if self.primary_gpu == -1:  # 无GPU可用
                return -1
            
            # 轮廓残缺补全优先分配到主GPU
            if "lunkuo_canque_fill" in module_info.name:
                print(f"🎯 {module_info.name} 优先分配到GPU{self.primary_gpu}")
                return self.primary_gpu
            
            # 其他模块根据显存使用情况分配
            primary_usage = self.gpu_memory_usage.get(self.primary_gpu, 0.0)
            secondary_usage = self.gpu_memory_usage.get(self.secondary_gpu, 0.0)
            
            # 简单的负载均衡策略
            if primary_usage <= secondary_usage:
                selected_gpu = self.primary_gpu
            else:
                selected_gpu = self.secondary_gpu
            
            # 更新显存使用估计
            memory_increment = 0.3 if module_info.memory_requirement == 'high' else 0.2
            self.gpu_memory_usage[selected_gpu] += memory_increment
            
            print(f"🎯 {module_info.name} 分配到GPU{selected_gpu}")
            return selected_gpu
    
    def release_gpu(self, gpu_id: int, module_info: ModuleInfo):
        """释放GPU资源"""
        with self.gpu_lock:
            if gpu_id in self.gpu_memory_usage:
                memory_decrement = 0.3 if module_info.memory_requirement == 'high' else 0.2
                self.gpu_memory_usage[gpu_id] = max(0.0, self.gpu_memory_usage[gpu_id] - memory_decrement)
                print(f"🔄 释放GPU{gpu_id}资源 ({module_info.name})")


class DetectionModuleWrapper:
    """检测模块统一包装器"""
    
    def __init__(self, module_info: ModuleInfo, gpu_manager: GPUResourceManager):
        self.module_info = module_info
        self.gpu_manager = gpu_manager
        self.module = None
        self.gpu_id = -1
        self.is_loaded = False
    
    def load_module(self) -> bool:
        """加载检测模块"""
        try:
            print(f"🔄 加载模块: {self.module_info.name}")
            
            # 分配GPU
            self.gpu_id = self.gpu_manager.allocate_gpu(self.module_info)
            
            # 动态导入模块
            module_path = self.module_info.module_path
            if os.path.exists(module_path):
                # 添加模块目录到路径
                module_dir = os.path.dirname(module_path)
                if module_dir not in sys.path:
                    sys.path.insert(0, module_dir)
                
                # 导入模块
                module_name = os.path.basename(module_path).replace('.py', '')
                self.module = __import__(module_name)
                
                print(f"✅ 成功加载模块: {self.module_info.name}")
                self.is_loaded = True
                return True
            else:
                print(f"❌ 模块文件不存在: {module_path}")
                return False
                
        except Exception as e:
            print(f"❌ 加载模块失败 {self.module_info.name}: {e}")
            return False
    
    def process_images(self, image_paths: List[str], output_dir: str,
                      threshold: float = 0.5) -> Dict[str, Any]:
        """处理图像列表"""
        if not self.is_loaded:
            return {"success": False, "error": "模块未加载"}

        try:
            print(f"🔄 {self.module_info.name} 开始处理 {len(image_paths)} 张图像")
            start_time = time.time()

            # 创建模块专用输出目录
            module_output_dir = os.path.join(output_dir, self.module_info.name)
            os.makedirs(module_output_dir, exist_ok=True)

            # 调用模块的处理函数
            if hasattr(self.module, 'run_det_gpu_final'):
                # 使用GPU处理函数
                success_count = self.module.run_det_gpu_final(
                    input_image_dir=os.path.dirname(image_paths[0]),
                    output_json_dir=module_output_dir,
                    threshold=threshold,
                    label_name=self.module_info.label_name
                )
            elif hasattr(self.module, 'main'):
                # 使用main函数 - 需要修改工作目录
                original_cwd = os.getcwd()
                try:
                    # 切换到模块目录
                    module_dir = os.path.dirname(self.module_info.module_path)
                    os.chdir(module_dir)
                    success_count = len(image_paths)  # 假设全部成功
                    self.module.main()
                finally:
                    # 恢复原工作目录
                    os.chdir(original_cwd)
            else:
                print(f"❌ 模块 {self.module_info.name} 没有可用的处理函数")
                return {"success": False, "error": "无可用处理函数"}

            processing_time = time.time() - start_time

            # 检查输出文件并复制到目标目录
            actual_output_dir = self._find_and_copy_outputs(module_output_dir)

            print(f"✅ {self.module_info.name} 处理完成: {success_count}/{len(image_paths)} 张图像, 耗时: {processing_time:.2f}秒")

            return {
                "success": True,
                "processed_count": success_count,
                "total_count": len(image_paths),
                "processing_time": processing_time,
                "output_dir": actual_output_dir
            }

        except Exception as e:
            print(f"❌ {self.module_info.name} 处理失败: {e}")
            return {"success": False, "error": str(e)}

    def _find_and_copy_outputs(self, target_dir: str) -> str:
        """查找模块输出文件并复制到目标目录"""
        try:
            # 模块可能的输出目录
            module_dir = os.path.dirname(self.module_info.module_path)
            possible_output_dirs = [
                os.path.join(module_dir, "test_output_gpu_final"),
                os.path.join(module_dir, "test_output_gpu_pipeline_v2"),
                os.path.join(module_dir, "test_output"),
                os.path.join(module_dir, "output")
            ]

            copied_files = 0
            for output_dir in possible_output_dirs:
                if os.path.exists(output_dir):
                    # 复制JSON文件
                    for file in os.listdir(output_dir):
                        if file.endswith('.json'):
                            src_path = os.path.join(output_dir, file)
                            dst_path = os.path.join(target_dir, file)

                            # 复制文件
                            import shutil
                            shutil.copy2(src_path, dst_path)
                            copied_files += 1
                            print(f"📋 复制输出文件: {file}")

            if copied_files > 0:
                print(f"✅ 成功复制 {copied_files} 个输出文件到 {target_dir}")
            else:
                print(f"⚠️  未找到输出文件，检查目录: {possible_output_dirs}")

            return target_dir

        except Exception as e:
            print(f"❌ 复制输出文件失败: {e}")
            return target_dir
    
    def unload_module(self):
        """卸载模块并释放资源"""
        if self.gpu_id != -1:
            self.gpu_manager.release_gpu(self.gpu_id, self.module_info)
        self.is_loaded = False
        print(f"🔄 卸载模块: {self.module_info.name}")


def get_detection_modules() -> List[ModuleInfo]:
    """获取所有检测模块信息"""
    base_path = "/home/<USER>/xm/code/coderebuild"
    
    modules = [
        ModuleInfo(
            name="zhumaidakai",
            module_path=f"{base_path}/seg_det_zhumaidakai/deploy_seg_det_zhumaidakai_onnx_gpu_final.py",
            label_name="zhumaidakai",
            memory_requirement="medium",
            gpu_preference=1,
            color=(0, 0, 255)  # 红色
        ),
        ModuleInfo(
            name="zhumaizoushi", 
            module_path=f"{base_path}/seg_det_zhumaizoushi/deploy_seg_det_zhumaizoushi_onnx_gpu_final.py",
            label_name="zhumaizoushi",
            memory_requirement="medium",
            gpu_preference=1,
            color=(255, 0, 0)  # 蓝色
        ),
        ModuleInfo(
            name="zheheng",
            module_path=f"{base_path}/seg_det_zheheng/deploy_seg_det_zheheng_onnx_gpu_final.py", 
            label_name="zheheng",
            memory_requirement="medium",
            gpu_preference=1,
            color=(0, 255, 0)  # 绿色
        ),
        ModuleInfo(
            name="zhimai",
            module_path=f"{base_path}/seg_det_zhimai/deploy_seg_det_zhimai_onnx_gpu_final.py",
            label_name="zhimai", 
            memory_requirement="medium",
            gpu_preference=1,
            color=(0, 255, 255)  # 黄色
        ),
        ModuleInfo(
            name="zhimaiqing",
            module_path=f"{base_path}/seg_det_zhimaiqing/deploy_seg_det_zhimaiqing_onnx_gpu_final.py",
            label_name="zhimaiqing",
            memory_requirement="medium", 
            gpu_preference=1,
            color=(255, 255, 0)  # 青色
        ),
        ModuleInfo(
            name="lunkuo_canque_fill",
            module_path=f"{base_path}/seg_det_lunkuo_canque_fill/deploy_seg_lunkuo_canque_fill_onnx_gpu_final.py",
            label_name="lunkuo_canque_fill",
            memory_requirement="high",  # 显存占用大
            gpu_preference=1,  # 优先GPU1
            color=(128, 0, 128)  # 紫色
        )
    ]
    
    return modules


def test_single_module(module_info: ModuleInfo, config: DetectionConfig) -> bool:
    """测试单个模块是否能正常生成JSON"""
    print(f"\n🧪 测试模块: {module_info.name}")
    print("="*50)
    
    try:
        # 创建GPU管理器
        gpu_manager = GPUResourceManager(config.gpu_primary, config.gpu_secondary)
        
        # 创建模块包装器
        wrapper = DetectionModuleWrapper(module_info, gpu_manager)
        
        # 加载模块
        if not wrapper.load_module():
            print(f"❌ 模块 {module_info.name} 加载失败")
            return False
        
        # 获取测试图像
        image_files = [f for f in os.listdir(config.input_dir) if f.endswith('.bmp')]
        if not image_files:
            print(f"❌ 测试目录中没有找到图像文件")
            return False
        
        # 只测试第一张图像
        test_image = os.path.join(config.input_dir, image_files[0])
        
        # 处理图像
        result = wrapper.process_images([test_image], config.output_dir, config.threshold)
        
        # 检查结果
        if result["success"]:
            # 检查是否生成了JSON文件
            module_output_dir = result["output_dir"]
            json_files = [f for f in os.listdir(module_output_dir) if f.endswith('.json')]
            
            if json_files:
                print(f"✅ 模块 {module_info.name} 测试成功，生成了 {len(json_files)} 个JSON文件")
                
                # 验证JSON格式
                test_json_path = os.path.join(module_output_dir, json_files[0])
                try:
                    with open(test_json_path, 'r', encoding='utf-8') as f:
                        json_data = json.load(f)
                    print(f"✅ JSON格式验证通过")
                    return True
                except Exception as e:
                    print(f"❌ JSON格式验证失败: {e}")
                    return False
            else:
                print(f"❌ 模块 {module_info.name} 没有生成JSON文件")
                return False
        else:
            print(f"❌ 模块 {module_info.name} 处理失败: {result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试模块 {module_info.name} 时发生异常: {e}")
        return False
    finally:
        # 清理资源
        if 'wrapper' in locals():
            wrapper.unload_module()


class PipelineScheduler:
    """流水线并行调度器"""

    def __init__(self, config: DetectionConfig):
        self.config = config
        self.gpu_manager = GPUResourceManager(config.gpu_primary, config.gpu_secondary)

    def run_parallel_detection(self, modules: List[ModuleInfo], image_paths: List[str]) -> Dict[str, Any]:
        """并行运行所有检测模块"""
        print(f"\n🚀 第二阶段：并行检测处理")
        print("="*50)
        print(f"🔧 使用 {len(modules)} 个模块并行处理 {len(image_paths)} 张图像")

        start_time = time.time()
        results = {}

        # 使用线程池并行处理
        with ThreadPoolExecutor(max_workers=self.config.max_workers) as executor:
            # 提交所有模块任务
            future_to_module = {}
            for module_info in modules:
                future = executor.submit(self._process_module, module_info, image_paths)
                future_to_module[future] = module_info

            # 收集结果
            for future in as_completed(future_to_module):
                module_info = future_to_module[future]
                try:
                    result = future.result(timeout=300)  # 5分钟超时
                    results[module_info.name] = result

                    if result["success"]:
                        print(f"✅ {module_info.name} 并行处理完成: {result['processed_count']} 张图像, 耗时: {result['processing_time']:.2f}秒")
                    else:
                        print(f"❌ {module_info.name} 并行处理失败: {result.get('error', '未知错误')}")

                except Exception as e:
                    print(f"❌ {module_info.name} 并行处理异常: {e}")
                    results[module_info.name] = {"success": False, "error": str(e)}

        total_time = time.time() - start_time

        # 统计结果
        successful_modules = [name for name, result in results.items() if result["success"]]
        failed_modules = [name for name, result in results.items() if not result["success"]]

        print(f"\n📊 并行处理结果:")
        print(f"✅ 成功: {len(successful_modules)} 个模块")
        print(f"❌ 失败: {len(failed_modules)} 个模块")
        print(f"⏱️  总耗时: {total_time:.2f}秒")

        if failed_modules:
            print("失败的模块:")
            for module_name in failed_modules:
                print(f"   - {module_name}: {results[module_name].get('error', '未知错误')}")

        return {
            "success": len(successful_modules) > 0,
            "total_time": total_time,
            "successful_modules": successful_modules,
            "failed_modules": failed_modules,
            "results": results
        }

    def _process_module(self, module_info: ModuleInfo, image_paths: List[str]) -> Dict[str, Any]:
        """处理单个模块"""
        wrapper = DetectionModuleWrapper(module_info, self.gpu_manager)

        try:
            # 加载模块
            if not wrapper.load_module():
                return {"success": False, "error": "模块加载失败"}

            # 处理图像
            result = wrapper.process_images(image_paths, self.config.output_dir, self.config.threshold)
            return result

        finally:
            # 清理资源
            wrapper.unload_module()

    def merge_detection_results(self, image_paths: List[str], successful_modules: List[str]) -> bool:
        """合并检测结果"""
        print(f"\n🔄 第三阶段：合并检测结果")
        print("="*50)

        try:
            for image_path in image_paths:
                image_name = os.path.basename(image_path)
                base_name = os.path.splitext(image_name)[0]

                # 创建合并的JSON结构
                merged_result = {
                    "version": "4.5.6",
                    "flags": {},
                    "shapes": [],
                    "imagePath": image_name,
                    "imageData": None,
                    "imageHeight": 0,
                    "imageWidth": 0,
                    "detection_info": {
                        "modules": successful_modules,
                        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                        "total_detections": 0
                    }
                }

                total_detections = 0

                # 合并各模块的检测结果
                for module_name in successful_modules:
                    module_json_path = os.path.join(
                        self.config.output_dir, module_name, f"{base_name}.json"
                    )

                    if os.path.exists(module_json_path):
                        try:
                            with open(module_json_path, 'r', encoding='utf-8') as f:
                                module_data = json.load(f)

                            # 添加模块的检测结果
                            if "shapes" in module_data:
                                for shape in module_data["shapes"]:
                                    # 添加模块信息到shape
                                    shape["module"] = module_name
                                    merged_result["shapes"].append(shape)
                                    total_detections += 1

                            # 更新图像尺寸信息
                            if "imageHeight" in module_data and module_data["imageHeight"] > 0:
                                merged_result["imageHeight"] = module_data["imageHeight"]
                                merged_result["imageWidth"] = module_data["imageWidth"]

                        except Exception as e:
                            print(f"⚠️  读取模块结果失败 {module_name}/{base_name}.json: {e}")
                    else:
                        print(f"⚠️  模块结果文件不存在: {module_json_path}")

                merged_result["detection_info"]["total_detections"] = total_detections

                # 保存合并结果
                merged_json_path = os.path.join(self.config.output_dir, f"{base_name}_merged.json")
                with open(merged_json_path, 'w', encoding='utf-8') as f:
                    json.dump(merged_result, f, ensure_ascii=False, indent=2)

                print(f"✅ 合并完成: {base_name}_merged.json ({total_detections} 个检测结果)")

            print(f"🎉 所有检测结果合并完成！")
            return True

        except Exception as e:
            print(f"❌ 合并检测结果失败: {e}")
            return False


def main():
    """主函数"""
    print("🔥🔥🔥 烟叶综合形态检测系统 - GPU流水线并行版本 🔥🔥🔥")
    print("="*80)

    # 创建配置
    config = DetectionConfig()

    # 验证输入输出目录
    if not os.path.exists(config.input_dir):
        print(f"❌ 输入目录不存在: {config.input_dir}")
        return False

    os.makedirs(config.output_dir, exist_ok=True)
    print(f"📁 输入目录: {config.input_dir}")
    print(f"📁 输出目录: {config.output_dir}")

    # 获取所有检测模块
    modules = get_detection_modules()
    print(f"🔧 检测到 {len(modules)} 个检测模块")

    # 获取图像文件列表
    image_files = [f for f in os.listdir(config.input_dir) if f.endswith('.bmp')]
    image_paths = [os.path.join(config.input_dir, f) for f in image_files]
    print(f"📷 找到 {len(image_paths)} 张图像")

    if not image_paths:
        print("❌ 没有找到图像文件")
        return False

    # 第一阶段：测试每个模块
    print(f"\n🧪 第一阶段：测试各个模块")
    print("="*50)

    successful_modules = []
    failed_modules = []

    for module_info in modules:
        if test_single_module(module_info, config):
            successful_modules.append(module_info)
        else:
            failed_modules.append(module_info)

    # 报告测试结果
    print(f"\n📊 模块测试结果:")
    print(f"✅ 成功: {len(successful_modules)} 个模块")
    print(f"❌ 失败: {len(failed_modules)} 个模块")

    if failed_modules:
        print("失败的模块:")
        for module in failed_modules:
            print(f"   - {module.name}")

    if not successful_modules:
        print("❌ 没有可用的检测模块，程序退出")
        return False

    print(f"\n✅ 第一阶段测试完成，{len(successful_modules)} 个模块可用")

    # 第二阶段：并行处理
    scheduler = PipelineScheduler(config)
    parallel_result = scheduler.run_parallel_detection(successful_modules, image_paths)

    if not parallel_result["success"]:
        print("❌ 并行处理失败")
        return False

    # 第三阶段：合并结果
    merge_success = scheduler.merge_detection_results(image_paths, parallel_result["successful_modules"])

    if not merge_success:
        print("❌ 结果合并失败")
        return False

    print(f"\n🎉 烟叶综合形态检测系统处理完成！")
    print(f"📊 处理统计:")
    print(f"   图像数量: {len(image_paths)}")
    print(f"   成功模块: {len(parallel_result['successful_modules'])}")
    print(f"   总耗时: {parallel_result['total_time']:.2f}秒")
    print(f"   输出目录: {config.output_dir}")

    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
