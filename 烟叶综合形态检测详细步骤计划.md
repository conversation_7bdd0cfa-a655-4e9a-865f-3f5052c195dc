# 烟叶综合形态检测详细步骤计划

## 📋 项目概述

创建一个综合的烟叶形态检测系统，将6个独立的检测模块集成到一个GPU流水线并行处理程序中，实现高效的批量检测和结果合并。

## 🎯 核心目标

1. **模块集成**: 将6个成熟的检测模块集成到统一框架
2. **GPU资源优化**: 智能分配GPU1(24G)和GPU0资源
3. **流水线并行**: 实现高效的并行处理架构
4. **结果合并**: 统一JSON格式输出和可视化

## 🔧 检测模块清单

| 序号 | 模块名称 | 文件路径 | 显存需求 | GPU分配策略 |
|------|----------|----------|----------|-------------|
| 1 | 主脉打开检测 | `/seg_det_zhumaidakai/deploy_seg_det_zhumaidakai_onnx_gpu_final.py` | 中等 | GPU1/GPU0 |
| 2 | 主脉走势检测 | `/seg_det_zhumaizoushi/deploy_seg_det_zhumaizoushi_onnx_gpu_final.py` | 中等 | GPU1/GPU0 |
| 3 | 折痕检测 | `/seg_det_zheheng/deploy_seg_det_zheheng_onnx_gpu_final.py` | 中等 | GPU1/GPU0 |
| 4 | 支脉检测 | `/seg_det_zhimai/deploy_seg_det_zhimai_onnx_gpu_final.py` | 中等 | GPU1/GPU0 |
| 5 | 支脉青检测 | `/seg_det_zhimaiqing/deploy_seg_det_zhimaiqing_onnx_gpu_final.py` | 中等 | GPU1/GPU0 |
| 6 | 轮廓残缺补全 | `/seg_det_lunkuo_canque_fill/deploy_seg_lunkuo_canque_fill_onnx_gpu_final.py` | **大** | **GPU1优先** |

## 📁 目录结构

```
/home/<USER>/xm/code/coderafactor/
├── comprehensive_det_onnx_gpu1.py          # 主程序
├── vis_comprehensive_det_onnx_gpu.py       # 可视化程序
├── test_data/
│   ├── vis/                                # 输入图像目录(5张测试图像)
│   └── test_output1/                       # 输出目录
│       ├── *.json                          # 合并后的检测结果
│       └── *.png                           # 可视化结果
└── 烟叶综合形态检测详细步骤计划.md          # 本文档
```

## 🚀 详细实施步骤

### 第一阶段：架构设计与模块分析

#### 步骤1.1：模块接口分析
- [ ] 分析每个检测模块的输入输出接口
- [ ] 确定统一的调用方式和参数格式
- [ ] 识别模块间的依赖关系和执行顺序

#### 步骤1.2：GPU资源评估
- [ ] 测试每个模块的显存占用情况
- [ ] 设计GPU资源分配策略
- [ ] 制定显存不足时的降级方案

#### 步骤1.3：数据流设计
- [ ] 设计统一的数据传递格式
- [ ] 规划中间结果的存储和传递
- [ ] 确定最终JSON合并格式

### 第二阶段：核心框架开发

#### 步骤2.1：GPU资源管理器
```python
class GPUResourceManager:
    """GPU资源智能分配管理器"""
    - 检测可用GPU设备
    - 监控显存使用情况
    - 动态分配模型到合适的GPU
    - 轮廓残缺补全优先分配到GPU1
```

#### 步骤2.2：检测模块包装器
```python
class DetectionModuleWrapper:
    """检测模块统一包装器"""
    - 标准化模块调用接口
    - 处理输入输出格式转换
    - 异常处理和重试机制
    - 性能监控和日志记录
```

#### 步骤2.3：流水线调度器
```python
class PipelineScheduler:
    """流水线并行调度器"""
    - 任务队列管理
    - 并行执行控制
    - 结果收集和合并
    - 进度监控和状态报告
```

### 第三阶段：模块集成实现

#### 步骤3.1：模块导入和初始化
- [ ] 安全导入所有检测模块
- [ ] 初始化各模块的ONNX模型
- [ ] 配置GPU设备分配
- [ ] 验证模块可用性

#### 步骤3.2：检测流水线实现
```python
def run_comprehensive_detection(image_path, output_dir):
    """综合检测主流程"""
    1. 图像预处理和验证
    2. GPU资源分配和模型加载
    3. 并行执行各检测模块
    4. 结果收集和格式统一
    5. JSON合并和保存
    6. 资源清理和释放
```

#### 步骤3.3：结果合并逻辑
- [ ] 设计统一的JSON结构
- [ ] 实现多模块结果合并
- [ ] 处理坐标系统一化
- [ ] 添加元数据和时间戳

### 第四阶段：可视化系统开发

#### 步骤4.1：可视化框架设计
```python
class ComprehensiveVisualizer:
    """综合检测结果可视化器"""
    - 多模块结果叠加显示
    - 不同检测类型颜色编码
    - 图例和标注信息
    - 高质量图像输出
```

#### 步骤4.2：可视化功能实现
- [ ] 主脉打开结果可视化（红色）
- [ ] 主脉走势结果可视化（蓝色）
- [ ] 折痕检测结果可视化（绿色）
- [ ] 支脉检测结果可视化（黄色）
- [ ] 支脉青检测结果可视化（青色）
- [ ] 轮廓残缺补全结果可视化（紫色）

### 第五阶段：性能优化与测试

#### 步骤5.1：性能优化
- [ ] GPU内存使用优化
- [ ] 批处理优化
- [ ] 并行度调优
- [ ] 缓存机制实现

#### 步骤5.2：错误处理和容错
- [ ] 模块加载失败处理
- [ ] GPU显存不足处理
- [ ] 检测结果异常处理
- [ ] 文件I/O错误处理

#### 步骤5.3：全面测试
- [ ] 单模块功能测试
- [ ] 集成系统测试
- [ ] 性能压力测试
- [ ] 边界条件测试

## 🔧 技术实现要点

### GPU资源分配策略
```python
GPU分配优先级：
1. 轮廓残缺补全 → GPU1（显存占用大）
2. 其他模块 → GPU1剩余空间
3. 显存不足模块 → GPU0
4. 动态监控和调整
```

### 并行处理架构
```python
并行策略：
1. 模块级并行：不同模块同时处理不同图像
2. 批处理并行：单个模块批量处理多张图像
3. GPU间负载均衡：动态分配任务到不同GPU
```

### 数据格式统一
```json
{
  "image_info": {
    "filename": "image.jpg",
    "width": 1920,
    "height": 1080,
    "timestamp": "2024-01-01T12:00:00"
  },
  "detection_results": {
    "zhumaidakai": [...],
    "zhumaizoushi": [...],
    "zheheng": [...],
    "zhimai": [...],
    "zhimaiqing": [...],
    "lunkuo_canque_fill": [...]
  },
  "processing_info": {
    "gpu_allocation": {...},
    "processing_time": {...},
    "model_versions": {...}
  }
}
```

## 📊 测试验证计划

### 测试数据
- **输入**: `/home/<USER>/xm/code/coderafactor/test_data/vis/` (5张图像)
- **输出**: `/home/<USER>/xm/code/coderafactor/test_data/test_output1/`

### 验证标准
1. **功能验证**: 所有模块正常执行，无错误
2. **性能验证**: GPU资源合理分配，处理时间可接受
3. **结果验证**: JSON格式正确，可视化效果良好
4. **稳定性验证**: 多次运行结果一致

### 测试命令
```bash
source ~/.bashrc && conda activate vllm && python comprehensive_det_onnx_gpu1.py
```

## 📝 交付物清单

1. **主程序**: `comprehensive_det_onnx_gpu1.py`
2. **可视化程序**: `vis_comprehensive_det_onnx_gpu.py`
3. **检测结果**: JSON格式的合并检测结果
4. **可视化结果**: 带标注的检测结果图像
5. **技术文档**: 使用说明和API文档
6. **测试报告**: 性能测试和功能验证报告

## ⚠️ 风险评估与应对

### 主要风险
1. **GPU显存不足**: 实现动态降级到CPU或GPU0
2. **模块兼容性**: 统一接口包装和异常处理
3. **性能瓶颈**: 并行优化和资源调度
4. **结果冲突**: 坐标系统一和结果合并逻辑

### 应对策略
1. **渐进式集成**: 逐个模块集成测试
2. **容错设计**: 单个模块失败不影响整体
3. **性能监控**: 实时监控资源使用情况
4. **回退机制**: 提供串行处理备选方案

## 🎯 成功标准

1. ✅ 所有6个检测模块成功集成
2. ✅ GPU资源智能分配正常工作
3. ✅ 5张测试图像全部处理成功
4. ✅ JSON结果格式正确且完整
5. ✅ 可视化效果清晰美观
6. ✅ 系统稳定性和性能达标
