#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于beiyong6、canque、canque_fill计算four_points的程序
分析deploy_seg_det_lunkuo_canque_fill.py中four_points的计算逻辑，
并基于已知的轮廓数据快速计算出four_points

关键功能：
1. 从JSON文件中提取beiyong6、canque、canque_fill数据
2. 基于轮廓分析计算四个关键点
3. 与原版four_points进行验证对比
4. 输出计算精度和误差分析
"""

import os
import sys
import json
import numpy as np
import cv2
from typing import List, Tuple, Dict, Any
from pathlib import Path

# 添加根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

try:
    import base_function as bf
    print("✅ 成功导入所有必要模块")
except ImportError as e:
    print(f"❌ 错误: 无法导入必要模块 {e}")
    sys.exit(1)


class FourPointsCalculator:
    """Four Points计算器 - 基于轮廓数据计算四个关键点"""
    
    def __init__(self, image_width=1152, image_height=512):
        """
        初始化计算器
        Args:
            image_width: 图像宽度 (默认1152)
            image_height: 图像高度 (默认512)
        """
        self.image_width = image_width
        self.image_height = image_height
        print(f"🔵 初始化Four Points计算器 - 图像尺寸: {image_width}x{image_height}")
    
    def load_json_data(self, json_path: str) -> Dict[str, Any]:
        """加载JSON文件数据"""
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return data
        except Exception as e:
            print(f"❌ 加载JSON文件失败: {json_path} - {e}")
            return {}
    
    def extract_shapes_by_label(self, json_data: Dict[str, Any], label: str) -> List[List[List[float]]]:
        """
        从JSON数据中提取指定标签的shapes
        Args:
            json_data: JSON数据
            label: 标签名称
        Returns:
            List[List[List[float]]]: 轮廓点集列表
        """
        shapes = json_data.get('shapes', [])
        label_shapes = []
        
        for shape in shapes:
            if shape.get('label') == label:
                points = shape.get('points', [])
                if points:
                    label_shapes.append(points)
        
        return label_shapes
    
    def relative_to_absolute(self, relative_points: List[List[float]]) -> List[List[int]]:
        """将相对坐标转换为绝对坐标"""
        absolute_points = []
        for point in relative_points:
            x = int(point[0] * self.image_width)
            y = int(point[1] * self.image_height)
            absolute_points.append([x, y])
        return absolute_points
    
    def absolute_to_relative(self, absolute_points: List[List[int]]) -> List[List[float]]:
        """将绝对坐标转换为相对坐标"""
        relative_points = []
        for point in absolute_points:
            x = point[0] / self.image_width
            y = point[1] / self.image_height
            relative_points.append([x, y])
        return relative_points
    
    def find_extreme_points_improved(self, contour_points: List[List[int]]) -> Tuple[List[int], List[int], List[int], List[int]]:
        """
        改进的四个极值点查找算法 - 更接近原版逻辑
        Args:
            contour_points: 轮廓点集 (绝对坐标)
        Returns:
            Tuple: (left1_point, left2_point, right1_point, right2_point)
        """
        if not contour_points:
            return [0, 0], [0, 0], [0, 0], [0, 0]

        # 转换为numpy数组便于计算
        points = np.array(contour_points)

        # 使用PCA分析主方向 - 模拟原版的PCA逻辑
        center = np.mean(points, axis=0)
        centered_points = points - center

        # 计算协方差矩阵
        cov_matrix = np.cov(centered_points.T)
        eigenvalues, eigenvectors = np.linalg.eig(cov_matrix)

        # 主方向向量
        main_direction = eigenvectors[:, np.argmax(eigenvalues)]

        # 将点投影到主方向上
        projections = np.dot(centered_points, main_direction)

        # 找到投影的极值点
        min_proj_idx = np.argmin(projections)
        max_proj_idx = np.argmax(projections)

        # 这两个点应该是烟叶的两端
        end_point1 = points[min_proj_idx]
        end_point2 = points[max_proj_idx]

        # 确定哪个是左端，哪个是右端
        if end_point1[0] < end_point2[0]:
            left_end = end_point1
            right_end = end_point2
            left_end_idx = min_proj_idx
            right_end_idx = max_proj_idx
        else:
            left_end = end_point2
            right_end = end_point1
            left_end_idx = max_proj_idx
            right_end_idx = min_proj_idx

        # 在左端附近找上下两点
        left_region_radius = 100  # 搜索半径
        left_distances = np.linalg.norm(points - left_end, axis=1)
        left_nearby_mask = left_distances <= left_region_radius
        left_nearby_points = points[left_nearby_mask]
        left_nearby_indices = np.where(left_nearby_mask)[0]

        if len(left_nearby_points) > 1:
            left_top_local_idx = np.argmin(left_nearby_points[:, 1])
            left_bottom_local_idx = np.argmax(left_nearby_points[:, 1])
            left1_point = left_nearby_points[left_top_local_idx]  # 左上
            left2_point = left_nearby_points[left_bottom_local_idx]  # 左下
        else:
            left1_point = left_end
            left2_point = left_end

        # 在右端附近找上下两点
        right_region_radius = 100  # 搜索半径
        right_distances = np.linalg.norm(points - right_end, axis=1)
        right_nearby_mask = right_distances <= right_region_radius
        right_nearby_points = points[right_nearby_mask]
        right_nearby_indices = np.where(right_nearby_mask)[0]

        if len(right_nearby_points) > 1:
            right_top_local_idx = np.argmin(right_nearby_points[:, 1])
            right_bottom_local_idx = np.argmax(right_nearby_points[:, 1])
            right1_point = right_nearby_points[right_top_local_idx]  # 右上
            right2_point = right_nearby_points[right_bottom_local_idx]  # 右下
        else:
            right1_point = right_end
            right2_point = right_end

        return left1_point.tolist(), left2_point.tolist(), right1_point.tolist(), right2_point.tolist()

    def find_extreme_points(self, contour_points: List[List[int]]) -> Tuple[List[int], List[int], List[int], List[int]]:
        """
        从轮廓点中找到四个极值点 - 使用改进算法
        """
        return self.find_extreme_points_improved(contour_points)
    
    def calculate_four_points_from_beiyong6(self, beiyong6_points: List[List[float]]) -> List[List[float]]:
        """
        基于beiyong6轮廓计算four_points
        Args:
            beiyong6_points: beiyong6轮廓点集 (相对坐标)
        Returns:
            List[List[float]]: four_points (相对坐标)
        """
        if not beiyong6_points:
            return [[0, 0], [0, 0], [0, 0], [0, 0]]
        
        # 转换为绝对坐标
        absolute_points = self.relative_to_absolute(beiyong6_points)
        
        # 找到四个极值点
        left1_point, left2_point, right1_point, right2_point = self.find_extreme_points(absolute_points)
        
        # 转换回相对坐标
        four_points = self.absolute_to_relative([left1_point, left2_point, right1_point, right2_point])
        
        return four_points
    
    def calculate_distance(self, point1: List[float], point2: List[float]) -> float:
        """计算两点之间的距离"""
        return np.sqrt((point1[0] - point2[0])**2 + (point1[1] - point2[1])**2)
    
    def compare_four_points(self, calculated: List[List[float]], original: List[List[float]]) -> Dict[str, Any]:
        """
        比较计算的four_points和原版的four_points
        Args:
            calculated: 计算得到的four_points
            original: 原版的four_points
        Returns:
            Dict: 比较结果
        """
        if len(calculated) != 4 or len(original) != 4:
            return {"error": "four_points数量不正确"}
        
        distances = []
        for i in range(4):
            dist = self.calculate_distance(calculated[i], original[i])
            distances.append(dist)
        
        max_distance = max(distances)
        avg_distance = sum(distances) / len(distances)
        
        return {
            "distances": distances,
            "max_distance": max_distance,
            "avg_distance": avg_distance,
            "point_names": ["left1_point", "left2_point", "right1_point", "right2_point"],
            "calculated": calculated,
            "original": original
        }
    
    def process_single_json(self, json_path: str) -> Dict[str, Any]:
        """
        处理单个JSON文件
        Args:
            json_path: JSON文件路径
        Returns:
            Dict: 处理结果
        """
        print(f"\n🔍 处理文件: {Path(json_path).name}")
        
        # 加载JSON数据
        json_data = self.load_json_data(json_path)
        if not json_data:
            return {"error": "无法加载JSON数据"}
        
        # 提取各种标签的数据
        beiyong6_shapes = self.extract_shapes_by_label(json_data, "beiyong6")
        canque_shapes = self.extract_shapes_by_label(json_data, "canque")
        canque_fill_shapes = self.extract_shapes_by_label(json_data, "canque_fill")
        four_points_shapes = self.extract_shapes_by_label(json_data, "four_points")
        
        print(f"   📊 数据统计:")
        print(f"      beiyong6: {len(beiyong6_shapes)} 个轮廓")
        print(f"      canque: {len(canque_shapes)} 个轮廓")
        print(f"      canque_fill: {len(canque_fill_shapes)} 个轮廓")
        print(f"      four_points: {len(four_points_shapes)} 个轮廓")
        
        # 获取原版four_points
        if not four_points_shapes:
            return {"error": "未找到原版four_points"}
        
        original_four_points = four_points_shapes[0]  # 假设只有一个four_points
        
        # 基于beiyong6计算four_points
        if beiyong6_shapes:
            beiyong6_points = beiyong6_shapes[0]  # 假设只有一个beiyong6
            calculated_four_points = self.calculate_four_points_from_beiyong6(beiyong6_points)
            
            # 比较结果
            comparison = self.compare_four_points(calculated_four_points, original_four_points)
            
            return {
                "file": Path(json_path).name,
                "beiyong6_points_count": len(beiyong6_points),
                "calculated_four_points": calculated_four_points,
                "original_four_points": original_four_points,
                "comparison": comparison
            }
        else:
            return {"error": "未找到beiyong6数据"}
    
    def process_directory(self, test_output_dir: str) -> List[Dict[str, Any]]:
        """
        处理整个test_output目录
        Args:
            test_output_dir: test_output目录路径
        Returns:
            List[Dict]: 所有文件的处理结果
        """
        print(f"🚀 开始处理目录: {test_output_dir}")
        
        results = []
        json_files = [f for f in os.listdir(test_output_dir) if f.endswith('.json') and not f.endswith('_bak')]
        
        print(f"📊 找到 {len(json_files)} 个JSON文件")
        
        for json_file in sorted(json_files):
            json_path = os.path.join(test_output_dir, json_file)
            result = self.process_single_json(json_path)
            results.append(result)
        
        return results
    
    def analyze_results(self, results: List[Dict[str, Any]]) -> None:
        """分析所有结果"""
        print("\n📈 结果分析:")
        print("="*60)
        
        successful_results = [r for r in results if "comparison" in r]
        
        if not successful_results:
            print("❌ 没有成功的计算结果")
            return
        
        all_max_distances = []
        all_avg_distances = []
        
        for result in successful_results:
            comparison = result["comparison"]
            max_dist = comparison["max_distance"]
            avg_dist = comparison["avg_distance"]
            
            all_max_distances.append(max_dist)
            all_avg_distances.append(avg_dist)
            
            print(f"\n📁 {result['file']}:")
            print(f"   beiyong6点数: {result['beiyong6_points_count']}")
            print(f"   最大误差: {max_dist:.6f}")
            print(f"   平均误差: {avg_dist:.6f}")
            
            # 显示每个点的误差
            for i, (name, dist) in enumerate(zip(comparison["point_names"], comparison["distances"])):
                print(f"   {name}: {dist:.6f}")
        
        # 总体统计
        print(f"\n📊 总体统计 ({len(successful_results)} 个文件):")
        print(f"   最大误差范围: {min(all_max_distances):.6f} - {max(all_max_distances):.6f}")
        print(f"   平均最大误差: {sum(all_max_distances)/len(all_max_distances):.6f}")
        print(f"   平均误差范围: {min(all_avg_distances):.6f} - {max(all_avg_distances):.6f}")
        print(f"   总体平均误差: {sum(all_avg_distances)/len(all_avg_distances):.6f}")


def main():
    """主函数"""
    try:
        print("🧪 Four Points计算器")
        print("="*60)
        
        # 配置路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        test_output_dir = os.path.join(current_dir, 'test_output')
        
        if not os.path.exists(test_output_dir):
            print(f"❌ test_output目录不存在: {test_output_dir}")
            return False
        
        # 创建计算器
        calculator = FourPointsCalculator()
        
        # 处理所有JSON文件
        results = calculator.process_directory(test_output_dir)
        
        # 分析结果
        calculator.analyze_results(results)
        
        print("\n🎉 Four Points计算完成！")
        return True
        
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
