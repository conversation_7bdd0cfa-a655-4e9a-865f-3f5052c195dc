#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
烟叶残缺补全等特征检测双GPU流水线并行版本
基于主脉打开检测的成功流水线架构，专门适配双模型双GPU分布式处理

作者: 系统架构师
日期: 2025-07-28
版本: 2.0.0

关键特性：
1. 双GPU流水线并行架构
2. 高性能内存池管理
3. 异步队列系统
4. 与现有程序完全一致的检测结果
5. canque模型在GPU 0，lunkuo模型在GPU 1
"""

import os
import sys
import time
import threading
import cv2
import numpy as np
import json
import queue
from typing import List, Dict, Optional, Any, Tuple
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 添加coderafactor目录到Python路径
coderafactor_root = "/home/<USER>/xm/code/coderafactor"
if coderafactor_root not in sys.path:
    sys.path.insert(0, coderafactor_root)

try:
    import base_function as bf
    from seg_det_lunkuo_canque_fill.deploy_seg_canque_onnx_gpu_final import GPUCanqueModelManager
    from seg_det_lunkuo_canque_fill.deploy_seg_lunkuo_onnx_gpu_final import GPULunKuoModelManager
    print("🔥🔥🔥 双GPU流水线并行版本 - 成功导入所有必要模块 🔥🔥🔥")
except ImportError as e:
    print(f"❌ 错误: 无法导入必要模块 {e}")
    sys.exit(1)


@dataclass
class PipelineData:
    """流水线数据结构"""
    image_path: str
    image_data: Optional[np.ndarray] = None
    preprocessed_data: Optional[np.ndarray] = None
    canque_result: Optional[np.ndarray] = None
    lunkuo_result: Optional[np.ndarray] = None
    processed_result: Optional[Dict] = None
    timestamp: float = 0.0
    stage: str = "init"  # init, preprocessed, inferred, postprocessed


class DualGPUMemoryPool:
    """双GPU内存池管理器 - 减少动态内存分配开销"""
    
    def __init__(self, pool_size: int = 8):
        self.pool_size = pool_size
        self.canque_buffers = queue.Queue()  # GPU 0 缓冲区
        self.lunkuo_buffers = queue.Queue()  # GPU 1 缓冲区
        self.used_buffers = set()
        self._lock = threading.Lock()
        
        # 预分配GPU内存缓冲区
        print(f"🔧 初始化双GPU内存池 - 池大小: {pool_size}")
        
        # Canque模型缓冲区 (640x640)
        for _ in range(pool_size):
            buffer = np.zeros((1, 3, 640, 640), dtype=np.float32)
            self.canque_buffers.put(buffer)
        
        # Lunkuo模型缓冲区 (1024x1024)
        for _ in range(pool_size):
            buffer = np.zeros((1, 3, 1024, 1024), dtype=np.float32)
            self.lunkuo_buffers.put(buffer)
    
    def get_canque_buffer(self) -> Optional[np.ndarray]:
        """获取canque模型的GPU内存缓冲区"""
        try:
            buffer = self.canque_buffers.get_nowait()
            with self._lock:
                self.used_buffers.add(id(buffer))
            return buffer
        except queue.Empty:
            print("⚠️  Canque GPU内存池已满，创建临时缓冲区")
            return np.zeros((1, 3, 640, 640), dtype=np.float32)
    
    def get_lunkuo_buffer(self) -> Optional[np.ndarray]:
        """获取lunkuo模型的GPU内存缓冲区"""
        try:
            buffer = self.lunkuo_buffers.get_nowait()
            with self._lock:
                self.used_buffers.add(id(buffer))
            return buffer
        except queue.Empty:
            print("⚠️  Lunkuo GPU内存池已满，创建临时缓冲区")
            return np.zeros((1, 3, 1024, 1024), dtype=np.float32)
    
    def return_canque_buffer(self, buffer: np.ndarray):
        """归还canque GPU内存缓冲区"""
        if buffer is not None:
            with self._lock:
                if id(buffer) in self.used_buffers:
                    self.used_buffers.remove(id(buffer))
                    try:
                        self.canque_buffers.put_nowait(buffer)
                    except queue.Full:
                        print("⚠️  Canque GPU内存池队列已满")
    
    def return_lunkuo_buffer(self, buffer: np.ndarray):
        """归还lunkuo GPU内存缓冲区"""
        if buffer is not None:
            with self._lock:
                if id(buffer) in self.used_buffers:
                    self.used_buffers.remove(id(buffer))
                    try:
                        self.lunkuo_buffers.put_nowait(buffer)
                    except queue.Full:
                        print("⚠️  Lunkuo GPU内存池队列已满")
    
    def get_pool_stats(self) -> Dict[str, int]:
        """获取内存池统计信息"""
        with self._lock:
            return {
                'canque_available': self.canque_buffers.qsize(),
                'lunkuo_available': self.lunkuo_buffers.qsize(),
                'used': len(self.used_buffers),
                'total': self.pool_size * 2
            }


class AsyncQueue:
    """异步队列 - 支持流水线各阶段的数据传递"""
    
    def __init__(self, maxsize: int = 16):
        self.queue = queue.Queue(maxsize=maxsize)
        self.maxsize = maxsize
        
    def put(self, item, timeout: float = 1.0) -> bool:
        """放入数据项"""
        try:
            self.queue.put(item, timeout=timeout)
            return True
        except queue.Full:
            return False
    
    def get(self, timeout: float = 1.0) -> Optional[Any]:
        """获取数据项"""
        try:
            return self.queue.get(timeout=timeout)
        except queue.Empty:
            return None
    
    def qsize(self) -> int:
        """获取队列大小"""
        return self.queue.qsize()
    
    def empty(self) -> bool:
        """检查队列是否为空"""
        return self.queue.empty()


class DualGPUModelManager:
    """双GPU模型管理器 - 管理canque和lunkuo两个模型在不同GPU上"""
    
    # 类级别的锁，确保线程安全的初始化
    _init_lock = threading.Lock()
    
    def __init__(self, canque_model_path: str, lunkuo_model_path: str, thread_id: Optional[int] = None):
        """
        初始化双GPU模型管理器
        
        Args:
            canque_model_path: canque模型路径
            lunkuo_model_path: lunkuo模型路径  
            thread_id: 线程ID，用于调试
        """
        self.thread_id = thread_id or threading.current_thread().ident
        self.canque_model_path = canque_model_path
        self.lunkuo_model_path = lunkuo_model_path
        
        # 模型管理器实例
        self.canque_manager: Optional[GPUCanqueModelManager] = None
        self.lunkuo_manager: Optional[GPULunKuoModelManager] = None
        
        # 初始化状态
        self._initialized = False
        
        print(f"🔧 [线程{self.thread_id}] 创建双GPU模型管理器")
        
        # 立即加载模型
        self._load_models()
    
    def _load_models(self) -> None:
        """
        加载两个模型到不同GPU - 线程安全
        """
        with self._init_lock:
            if self._initialized:
                return
                
            print(f"🔵 [线程{self.thread_id}] 开始加载双GPU模型...")
            
            # 验证模型文件存在
            if not os.path.exists(self.canque_model_path):
                raise FileNotFoundError(f"Canque模型文件不存在: {self.canque_model_path}")
            if not os.path.exists(self.lunkuo_model_path):
                raise FileNotFoundError(f"Lunkuo模型文件不存在: {self.lunkuo_model_path}")
            
            # 加载canque模型到GPU 0
            print(f"🔵 [线程{self.thread_id}] 加载canque模型到GPU 0...")
            self.canque_manager = GPUCanqueModelManager(
                model_path=self.canque_model_path,
                thread_id=self.thread_id
            )
            
            # 加载lunkuo模型到GPU 1
            print(f"🔵 [线程{self.thread_id}] 加载lunkuo模型到GPU 1...")
            self.lunkuo_manager = GPULunKuoModelManager(
                model_path=self.lunkuo_model_path,
                thread_id=self.thread_id
            )
            
            self._initialized = True
            print(f"✅ [线程{self.thread_id}] 双GPU模型加载完成")
    
    def predict_canque(self, image: np.ndarray) -> np.ndarray:
        """在GPU 0上运行canque模型推理"""
        if not self._initialized:
            raise RuntimeError("模型未正确初始化")
        return self.canque_manager.run_inference(image, score_th=None)
    
    def predict_lunkuo(self, image: np.ndarray) -> np.ndarray:
        """在GPU 1上运行lunkuo模型推理"""
        if not self._initialized:
            raise RuntimeError("模型未正确初始化")
        return self.lunkuo_manager.run_inference(image, score_th=None)


class MaskProcessor:
    """
    Mask处理器 - 负责mask叠加、轮廓提取、坐标转换
    与现有程序完全一致的处理逻辑
    """

    def __init__(self, target_size: Tuple[int, int] = (1152, 512)):
        """
        初始化Mask处理器

        Args:
            target_size: 目标尺寸 (width, height)
        """
        self.target_width, self.target_height = target_size
        print(f"🔧 初始化Mask处理器 - 目标尺寸: {self.target_width}x{self.target_height}")

    def process_combined_masks(self, canque_mask: np.ndarray, lunkuo_mask: np.ndarray) -> List[List[float]]:
        """
        处理组合mask，提取canque_fill轮廓 - 与现有程序完全一致

        Args:
            canque_mask: canque模型输出的mask
            lunkuo_mask: lunkuo模型输出的mask

        Returns:
            canque_fill轮廓的相对坐标点集
        """
        # 1. 调整mask尺寸到目标大小
        canque_resized = cv2.resize(canque_mask, (self.target_width, self.target_height))
        lunkuo_resized = cv2.resize(lunkuo_mask, (self.target_width, self.target_height))

        # 2. 二值化处理
        _, canque_binary = cv2.threshold(canque_resized, 127, 255, cv2.THRESH_BINARY)
        _, lunkuo_binary = cv2.threshold(lunkuo_resized, 127, 255, cv2.THRESH_BINARY)

        # 3. 叠加两个mask
        combined_mask = cv2.bitwise_or(canque_binary, lunkuo_binary)

        # 4. 提取最大外轮廓
        contours, _ = cv2.findContours(combined_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        if not contours:
            return []

        # 找到最大轮廓
        max_contour = max(contours, key=cv2.contourArea)

        # 5. 转换为相对坐标
        points = max_contour.reshape(-1, 2)
        relative_points = [[float(x) / self.target_width, float(y) / self.target_height] for x, y in points]

        return relative_points

    def extract_individual_contours(self, canque_mask: np.ndarray, lunkuo_mask: np.ndarray) -> Tuple[List[List[List[float]]], List[List[float]]]:
        """
        提取单独的轮廓坐标 - 与现有程序完全一致

        Args:
            canque_mask: canque模型输出的mask
            lunkuo_mask: lunkuo模型输出的mask

        Returns:
            Tuple[canque轮廓列表, beiyong6轮廓]: canque多个轮廓的相对坐标点集列表和beiyong6单个轮廓
        """
        # 调整尺寸
        canque_resized = cv2.resize(canque_mask, (self.target_width, self.target_height))
        lunkuo_resized = cv2.resize(lunkuo_mask, (self.target_width, self.target_height))

        # 提取canque多个轮廓
        canque_contours = self._extract_multiple_contours_from_mask(canque_resized)

        # 提取beiyong6单个最大轮廓
        beiyong6_contours = self._extract_single_contour_from_mask(lunkuo_resized)

        return canque_contours, beiyong6_contours

    def _extract_multiple_contours_from_mask(self, mask: np.ndarray) -> List[List[List[float]]]:
        """
        从mask中提取多个轮廓 - 与现有程序完全一致

        Args:
            mask: 输入mask

        Returns:
            多个轮廓的相对坐标点集列表
        """
        # 二值化处理
        _, binary = cv2.threshold(mask, 127, 255, cv2.THRESH_BINARY)

        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        if not contours:
            return []

        # 为每个轮廓提取点集并转换为相对坐标
        all_contours_points = []
        for contour in contours:
            contour_points = []
            for point in contour:
                x = int(point[0][0]) / self.target_width
                y = int(point[0][1]) / self.target_height
                contour_points.append([x, y])

            # 只保留有足够点数的轮廓
            if len(contour_points) >= 3:
                all_contours_points.append(contour_points)

        return all_contours_points

    def _extract_single_contour_from_mask(self, mask: np.ndarray) -> List[List[float]]:
        """
        从mask中提取单个最大轮廓 - 与现有程序完全一致

        Args:
            mask: 输入mask

        Returns:
            单个轮廓的相对坐标点集
        """
        # 二值化处理
        _, binary = cv2.threshold(mask, 127, 255, cv2.THRESH_BINARY)

        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        if not contours:
            return []

        # 找到最大轮廓
        max_contour = max(contours, key=cv2.contourArea)

        # 转换为相对坐标
        contour_points = []
        for point in max_contour:
            x = int(point[0][0]) / self.target_width
            y = int(point[0][1]) / self.target_height
            contour_points.append([x, y])

        return contour_points

    def calculate_four_points_from_beiyong6(self, beiyong6_points: List[List[float]]) -> List[List[float]]:
        """
        基于beiyong6轮廓计算four_points - 集成cal_four_points.py的逻辑
        Args:
            beiyong6_points: beiyong6轮廓点集 (相对坐标)
        Returns:
            List[List[float]]: four_points (相对坐标)
        """
        if not beiyong6_points:
            return [[0, 0], [0, 0], [0, 0], [0, 0]]

        # 转换为绝对坐标
        absolute_points = []
        for point in beiyong6_points:
            x = int(point[0] * self.target_width)
            y = int(point[1] * self.target_height)
            absolute_points.append([x, y])

        # 找到四个极值点
        left1_point, left2_point, right1_point, right2_point = self._find_extreme_points(absolute_points)

        # 转换回相对坐标
        four_points = []
        for point in [left1_point, left2_point, right1_point, right2_point]:
            x = point[0] / self.target_width
            y = point[1] / self.target_height
            four_points.append([x, y])

        return four_points

    def _find_extreme_points(self, contour_points: List[List[int]]) -> Tuple[List[int], List[int], List[int], List[int]]:
        """
        从轮廓点中找到四个极值点 - 基于cal_four_points.py的改进算法
        Args:
            contour_points: 轮廓点集 (绝对坐标)
        Returns:
            Tuple: (left1_point, left2_point, right1_point, right2_point)
        """
        if not contour_points:
            return [0, 0], [0, 0], [0, 0], [0, 0]

        # 转换为numpy数组便于计算
        points = np.array(contour_points)

        # 使用PCA分析主方向 - 模拟原版的PCA逻辑
        center = np.mean(points, axis=0)
        centered_points = points - center

        # 计算协方差矩阵
        cov_matrix = np.cov(centered_points.T)
        eigenvalues, eigenvectors = np.linalg.eig(cov_matrix)

        # 主方向向量
        main_direction = eigenvectors[:, np.argmax(eigenvalues)]

        # 将点投影到主方向上
        projections = np.dot(centered_points, main_direction)

        # 找到投影的极值点
        min_proj_idx = np.argmin(projections)
        max_proj_idx = np.argmax(projections)

        # 这两个点应该是烟叶的两端
        end_point1 = points[min_proj_idx]
        end_point2 = points[max_proj_idx]

        # 确定哪个是左端，哪个是右端
        if end_point1[0] < end_point2[0]:
            left_end = end_point1
            right_end = end_point2
        else:
            left_end = end_point2
            right_end = end_point1

        # 在左端附近找上下两点
        left_region_radius = 100  # 搜索半径
        left_distances = np.linalg.norm(points - left_end, axis=1)
        left_nearby_mask = left_distances <= left_region_radius
        left_nearby_points = points[left_nearby_mask]

        if len(left_nearby_points) > 1:
            left_top_local_idx = np.argmin(left_nearby_points[:, 1])
            left_bottom_local_idx = np.argmax(left_nearby_points[:, 1])
            left1_point = left_nearby_points[left_top_local_idx]  # 左上
            left2_point = left_nearby_points[left_bottom_local_idx]  # 左下
        else:
            left1_point = left_end
            left2_point = left_end

        # 在右端附近找上下两点
        right_region_radius = 100  # 搜索半径
        right_distances = np.linalg.norm(points - right_end, axis=1)
        right_nearby_mask = right_distances <= right_region_radius
        right_nearby_points = points[right_nearby_mask]

        if len(right_nearby_points) > 1:
            right_top_local_idx = np.argmin(right_nearby_points[:, 1])
            right_bottom_local_idx = np.argmax(right_nearby_points[:, 1])
            right1_point = right_nearby_points[right_top_local_idx]  # 右上
            right2_point = right_nearby_points[right_bottom_local_idx]  # 右下
        else:
            right1_point = right_end
            right2_point = right_end

        return left1_point.tolist(), left2_point.tolist(), right1_point.tolist(), right2_point.tolist()


class DualGPUPipelineProcessor:
    """双GPU流水线处理器 - 真正的并行流水线架构"""

    def __init__(self, model_manager: 'DualGPUModelManager',
                 preprocess_workers: int = 2, postprocess_workers: int = 2,
                 queue_size: int = 16):
        self.model_manager = model_manager
        self.preprocess_workers = preprocess_workers
        self.postprocess_workers = postprocess_workers

        # 初始化队列系统
        self.preprocess_queue = AsyncQueue(queue_size)
        self.inference_queue = AsyncQueue(queue_size)
        self.postprocess_queue = AsyncQueue(queue_size)
        self.result_queue = AsyncQueue(queue_size)

        # 初始化双GPU内存池
        self.memory_pool = DualGPUMemoryPool(pool_size=queue_size)

        # 初始化Mask处理器
        self.mask_processor = MaskProcessor()

        # 控制标志
        self._stop_event = threading.Event()
        self._threads = []

        # 统计信息
        self.stats = {
            'processed_count': 0,
            'preprocess_time': 0.0,
            'inference_time': 0.0,
            'postprocess_time': 0.0,
            'total_time': 0.0
        }

        print(f"🚀 初始化双GPU流水线处理器 - 预处理工作线程: {preprocess_workers}, 后处理工作线程: {postprocess_workers}")

    def _preprocess_worker(self, worker_id: int):
        """预处理工作线程"""
        print(f"🔵 启动预处理工作线程 {worker_id}")

        while not self._stop_event.is_set():
            # 从预处理队列获取任务
            item = self.preprocess_queue.get(timeout=0.1)
            if item is None:
                continue

            try:
                start_time = time.time()

                # 读取图像 - 与现有版本完全一致，不进行尺寸调整
                image = cv2.imread(item.image_path)
                if image is None:
                    print(f"⚠️  [预处理{worker_id}] 无法读取图像: {item.image_path}")
                    continue

                # 直接使用原始图像，不进行尺寸调整
                item.image_data = image

                item.stage = "preprocessed"
                item.timestamp = time.time()

                # 放入推理队列
                if not self.inference_queue.put(item, timeout=1.0):
                    print(f"⚠️  [预处理{worker_id}] 推理队列已满，丢弃任务")
                    continue

                preprocess_time = time.time() - start_time
                self.stats['preprocess_time'] += preprocess_time

            except Exception as e:
                print(f"❌ [预处理{worker_id}] 处理异常: {e}")
                continue

        print(f"🔵 预处理工作线程 {worker_id} 已停止")

    def _dual_inference_worker(self):
        """双GPU推理工作线程 - 并行执行两个模型"""
        print(f"🔶 启动双GPU推理工作线程")

        while not self._stop_event.is_set():
            # 从推理队列获取任务
            item = self.inference_queue.get(timeout=0.1)
            if item is None:
                continue

            try:
                start_time = time.time()

                # 并行推理两个模型
                with ThreadPoolExecutor(max_workers=2, thread_name_prefix="DualGPUInference") as executor:
                    # 提交两个推理任务到不同GPU
                    canque_future = executor.submit(self.model_manager.predict_canque, item.image_data)
                    lunkuo_future = executor.submit(self.model_manager.predict_lunkuo, item.image_data)

                    # 等待结果
                    canque_result = canque_future.result()
                    lunkuo_result = lunkuo_future.result()

                if canque_result is None or lunkuo_result is None:
                    print(f"⚠️  [推理] 双GPU推理失败: {item.image_path}")
                    continue

                item.canque_result = canque_result
                item.lunkuo_result = lunkuo_result
                item.stage = "inferred"

                # 放入后处理队列
                if not self.postprocess_queue.put(item, timeout=1.0):
                    print(f"⚠️  [推理] 后处理队列已满，丢弃任务")
                    continue

                inference_time = time.time() - start_time
                self.stats['inference_time'] += inference_time

            except Exception as e:
                print(f"❌ [推理] 处理异常: {e}")
                continue

        print(f"🔶 双GPU推理工作线程已停止")

    def _postprocess_worker(self, worker_id: int, json_dir: str, vis_dir: str):
        """后处理工作线程"""
        print(f"🟢 启动后处理工作线程 {worker_id}")
        # vis_dir参数保留用于未来可视化功能扩展
        _ = vis_dir  # 避免未使用参数警告

        while not self._stop_event.is_set():
            # 从后处理队列获取任务
            item = self.postprocess_queue.get(timeout=0.1)
            if item is None:
                continue

            try:
                start_time = time.time()

                # 处理组合mask，提取canque_fill轮廓
                canque_fill_points = self.mask_processor.process_combined_masks(
                    item.canque_result, item.lunkuo_result
                )

                # 提取单独的轮廓
                canque_contours, beiyong6_points = self.mask_processor.extract_individual_contours(
                    item.canque_result, item.lunkuo_result
                )

                # 生成shapes - 与现有版本完全一致的方式
                shapes = []

                # 添加canque轮廓 - 使用与现有版本相同的逻辑
                if canque_contours:
                    for contour_points in canque_contours:
                        if contour_points:  # 确保轮廓点不为空
                            shape = {
                                "label": "canque",
                                "points": contour_points,
                                "group_id": None,
                                "shape_type": "polygon",
                                "flags": {},
                                "other_data": {}
                            }
                            shapes.append(shape)

                # 添加beiyong6轮廓
                if beiyong6_points:
                    shape = {
                        "label": "beiyong6",
                        "points": beiyong6_points,
                        "group_id": None,
                        "shape_type": "polygon",
                        "flags": {},
                        "other_data": {}
                    }
                    shapes.append(shape)

                # 添加canque_fill轮廓
                if canque_fill_points:
                    shape = {
                        "label": "canque_fill",
                        "points": canque_fill_points,
                        "group_id": None,
                        "shape_type": "polygon",
                        "flags": {},
                        "other_data": {}
                    }
                    shapes.append(shape)

                # 计算并添加four_points轮廓 - 基于beiyong6计算
                if beiyong6_points:
                    four_points = self.mask_processor.calculate_four_points_from_beiyong6(beiyong6_points)
                    if four_points and len(four_points) == 4:
                        shape = {
                            "label": "four_points",
                            "points": four_points,
                            "group_id": None,
                            "shape_type": "polygon",
                            "flags": {},
                            "other_data": {}
                        }
                        shapes.append(shape)

                # 保存JSON - 与现有版本完全一致的方式
                if shapes:
                    json_name = bf.get_file_name(bf.rename_add_post(item.image_path, post="json"))
                    json_path = os.path.join(json_dir, json_name)

                    # 创建基础JSON结构 - 与现有版本完全一致
                    json_data = {
                        "version": "4.5.6",
                        "flags": {},
                        "shapes": shapes,
                        "imagePath": bf.get_file_name(item.image_path),
                        "imageData": None,
                        "imageHeight": 512,
                        "imageWidth": 1152
                    }

                    # 使用标准JSON保存 - 与现有版本一致
                    with open(json_path, 'w', encoding='utf-8') as f:
                        json.dump(json_data, f, ensure_ascii=False, indent=2)

                # 统计信息
                canque_count = len(canque_contours)
                canque_total_points = sum(len(contour) for contour in canque_contours)
                beiyong6_count = len(beiyong6_points)
                canque_fill_count = len(canque_fill_points)

                # 计算four_points用于统计
                four_points_count = 0
                if beiyong6_points:
                    four_points = self.mask_processor.calculate_four_points_from_beiyong6(beiyong6_points)
                    four_points_count = len(four_points) if four_points else 0

                print(f"✅ [后处理{worker_id}] 处理完成: {bf.get_file_name(item.image_path)}")
                print(f"   - canque轮廓数: {canque_count}, 总点数: {canque_total_points}")
                print(f"   - beiyong6点数: {beiyong6_count}")
                print(f"   - canque_fill点数: {canque_fill_count}")
                print(f"   - four_points点数: {four_points_count}")

                # 保存处理结果
                four_points = []
                if beiyong6_points:
                    four_points = self.mask_processor.calculate_four_points_from_beiyong6(beiyong6_points)

                item.processed_result = {
                    'canque_contours': canque_contours,
                    'beiyong6_points': beiyong6_points,
                    'canque_fill_points': canque_fill_points,
                    'four_points': four_points,
                    'shapes': shapes
                }
                item.stage = "postprocessed"

                # 放入结果队列
                self.result_queue.put(item, timeout=1.0)

                postprocess_time = time.time() - start_time
                self.stats['postprocess_time'] += postprocess_time
                self.stats['processed_count'] += 1

            except Exception as e:
                print(f"❌ [后处理{worker_id}] 处理异常: {e}")
                continue

        print(f"🟢 后处理工作线程 {worker_id} 已停止")

    def start_pipeline(self, json_dir: str, vis_dir: str):
        """启动流水线处理"""
        print("🚀 启动双GPU流水线处理...")

        # 启动预处理工作线程
        for i in range(self.preprocess_workers):
            thread = threading.Thread(target=self._preprocess_worker, args=(i,))
            thread.daemon = True
            thread.start()
            self._threads.append(thread)

        # 启动双GPU推理工作线程
        thread = threading.Thread(target=self._dual_inference_worker)
        thread.daemon = True
        thread.start()
        self._threads.append(thread)

        # 启动后处理工作线程
        for i in range(self.postprocess_workers):
            thread = threading.Thread(target=self._postprocess_worker,
                                    args=(i, json_dir, vis_dir))
            thread.daemon = True
            thread.start()
            self._threads.append(thread)

        print(f"✅ 双GPU流水线已启动 - 总线程数: {len(self._threads)}")

    def stop_pipeline(self):
        """停止流水线处理"""
        print("🛑 停止双GPU流水线处理...")
        self._stop_event.set()

        # 等待所有线程结束
        for thread in self._threads:
            thread.join(timeout=2.0)

        print("✅ 双GPU流水线已停止")

    def process_images_pipeline(self, image_paths: List[str], json_dir: str, vis_dir: str) -> int:
        """使用双GPU流水线处理图像列表"""
        if not image_paths:
            return 0

        total_start_time = time.time()

        # 启动流水线
        self.start_pipeline(json_dir, vis_dir)

        try:
            # 将所有图像路径放入预处理队列
            print(f"📥 将 {len(image_paths)} 张图像放入处理队列...")
            for image_path in image_paths:
                item = PipelineData(image_path=image_path, timestamp=time.time())

                # 等待队列有空间
                while not self.preprocess_queue.put(item, timeout=0.1):
                    if self._stop_event.is_set():
                        break
                    time.sleep(0.01)

            # 等待所有任务完成
            print("⏳ 等待所有任务完成...")
            processed_count = 0
            total_detections = 0

            # 监控处理进度
            last_progress_time = time.time()
            while processed_count < len(image_paths):
                result_item = self.result_queue.get(timeout=1.0)
                if result_item is not None:
                    processed_count += 1
                    if result_item.processed_result:
                        total_detections += len(result_item.processed_result.get('shapes', []))

                    # 显示进度（每5秒或每10%显示一次）
                    current_time = time.time()
                    progress = (processed_count / len(image_paths)) * 100
                    if (current_time - last_progress_time > 5.0) or (processed_count % max(1, len(image_paths) // 10) == 0):
                        # 获取队列状态
                        preprocess_size = self.preprocess_queue.qsize()
                        inference_size = self.inference_queue.qsize()
                        postprocess_size = self.postprocess_queue.qsize()
                        memory_stats = self.memory_pool.get_pool_stats()

                        print(f"📊 进度: {processed_count}/{len(image_paths)} ({progress:.1f}%) | "
                              f"队列: 预处理{preprocess_size} 推理{inference_size} 后处理{postprocess_size} | "
                              f"内存池: GPU0:{memory_stats['canque_available']} GPU1:{memory_stats['lunkuo_available']}")
                        last_progress_time = current_time

                # 检查是否超时
                if time.time() - total_start_time > 300:  # 5分钟超时
                    print("⚠️  处理超时，强制停止")
                    break

            total_time = time.time() - total_start_time
            self.stats['total_time'] = total_time

            # 输出性能统计
            self._print_performance_stats(len(image_paths), total_detections, total_time)

            return total_detections

        finally:
            # 停止流水线
            self.stop_pipeline()

    def _print_performance_stats(self, total_images: int, total_detections: int, total_time: float):
        """打印性能统计信息"""
        print(f"\n📊 双GPU流水线性能统计:")
        print(f"   总图像数量: {total_images}")
        print(f"   总检测数量: {total_detections}")
        print(f"   总处理时间: {total_time:.2f}秒")
        print(f"   平均每张图像: {total_time/total_images:.2f}秒")
        print(f"   处理速度: {total_images/total_time:.2f} 张/秒")

        if self.stats['processed_count'] > 0:
            avg_preprocess = self.stats['preprocess_time'] / self.stats['processed_count']
            avg_inference = self.stats['inference_time'] / self.stats['processed_count']
            avg_postprocess = self.stats['postprocess_time'] / self.stats['processed_count']

            print(f"   平均预处理时间: {avg_preprocess:.3f}秒")
            print(f"   平均双GPU推理时间: {avg_inference:.3f}秒")
            print(f"   平均后处理时间: {avg_postprocess:.3f}秒")

            # 计算并行效率
            sequential_time = avg_preprocess + avg_inference + avg_postprocess
            parallel_efficiency = (sequential_time / (total_time/total_images)) * 100
            print(f"   并行效率: {parallel_efficiency:.1f}%")


def main():
    """主函数"""
    try:
        print("🔥🔥🔥 这是双GPU流水线并行版本 - 全新架构 🔥🔥🔥")
        print("🔥🔥🔥 执行main()函数 - 确认执行正确版本 🔥🔥🔥")
        print("🚀 烟叶残缺补全等特征检测双GPU流水线并行版本")
        print("="*60)

        # 配置路径
        current_dir = os.path.dirname(os.path.abspath(__file__))

        # 输入路径配置
        input_image_dir = os.path.join(current_dir, 'test_images')
        output_json_dir = os.path.join(current_dir, 'test_output_gpu_pipeline')
        output_vis_dir = os.path.join(current_dir, 'test_output_gpu_pipeline')

        # 路径标准化
        input_image_dir = os.path.abspath(input_image_dir)
        output_json_dir = os.path.abspath(output_json_dir)
        output_vis_dir = os.path.abspath(output_vis_dir)

        # 设置模型路径
        canque_model_path = os.path.join(current_dir, "canque.onnx")
        lunkuo_model_path = os.path.join(current_dir, "lunkuo.onnx")

        # 设置输出目录
        os.makedirs(output_json_dir, exist_ok=True)
        os.makedirs(output_vis_dir, exist_ok=True)

        # 验证输入目录
        if not os.path.exists(input_image_dir):
            print(f"❌ 输入目录不存在: {input_image_dir}")
            return False

        # 验证模型文件
        if not os.path.exists(canque_model_path):
            print(f"❌ Canque模型文件不存在: {canque_model_path}")
            return False
        if not os.path.exists(lunkuo_model_path):
            print(f"❌ Lunkuo模型文件不存在: {lunkuo_model_path}")
            return False

        # 获取图像文件列表
        image_files = []
        for ext in ['.bmp', '.png', '.jpg', '.jpeg']:
            image_files.extend([f for f in os.listdir(input_image_dir) if f.lower().endswith(ext)])

        if not image_files:
            print(f"❌ 在目录中未找到图像文件: {input_image_dir}")
            return False

        print(f"📊 找到 {len(image_files)} 个图像文件")

        # 初始化双GPU模型管理器
        current_thread_id = threading.current_thread().ident
        print(f"🔧 [线程{current_thread_id}] 初始化双GPU模型管理器...")

        model_load_start = time.time()
        model_manager = DualGPUModelManager(canque_model_path, lunkuo_model_path, thread_id=current_thread_id)
        model_load_time = time.time() - model_load_start
        print(f"✅ 模型加载完成，耗时: {model_load_time:.3f}秒")

        # 使用双GPU流水线并行处理器
        pipeline_processor = DualGPUPipelineProcessor(
            model_manager,
            preprocess_workers=2,  # 2个预处理线程
            postprocess_workers=2  # 2个后处理线程
        )

        # 构建图像路径列表
        image_paths = [os.path.join(input_image_dir, image_file) for image_file in image_files]

        print(f"\n🚀 开始双GPU流水线并行处理...")

        # 双GPU流水线并行处理所有图像
        total_start_time = time.time()
        total_detections = pipeline_processor.process_images_pipeline(
            image_paths, output_json_dir, output_vis_dir
        )
        total_time = time.time() - total_start_time

        print(f"\n🎉 双GPU流水线并行处理完成！")
        print(f"📊 总处理时间: {total_time:.2f}秒")
        print(f"📊 平均每张图像: {total_time/len(image_files):.2f}秒")
        print(f"📊 处理吞吐量: {len(image_files)/total_time:.2f}张/秒")
        print(f"📊 总检测数量: {total_detections}")
        print(f"🚀 双GPU流水线并行架构 - canque(GPU0)/lunkuo(GPU1)并行执行")

        return True

    except Exception as e:
        print(f"❌ 主函数执行异常: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    main()
