# 烟叶综合杂色检测详细步骤计划

## 项目概述

将6个成熟的烟叶检测模块集成到一个综合检测程序中，实现GPU流水线并行处理，在单GPU(24GB显存)上高效运行。

## 集成模块清单

| 序号 | 检测类型 | 模块路径 | 模型文件 | 特点 |
|------|----------|----------|----------|------|
| 1 | 焦点浮青等检测 | `/seg_det_jiaodian/deploy_seg_det_condition_onnx_gpu_final.py` | condition.onnx | 多类别检测 |
| 2 | 横纹检测 | `/seg_det_hengwen/deploy_seg_det_hengwen_onnx_gpu_final.py` | hengwen.onnx | 分割+检测 |
| 3 | 烤红检测 | `/seg_det_kaohong/deploy_seg_det_kaohong_onnx_gpu_final.py` | kaohong.onnx | YOLO检测 |
| 4 | 挂灰检测 | `/seg_det_kaohong/deploy_seg_det_guahui_onnx_gpu_final.py` | guahui.onnx | YOLO检测 |
| 5 | 皱缩检测 | `/seg_det_zhousuo/deploy_seg_det_zhousuo_onnx_gpu_final.py` | zhousuo.onnx | YOLO检测 |
| 6 | 潮红检测 | `/seg_det_chaohong/deploy_seg_det_chaohong_onnx_gpu_final.py` | chaohong.onnx | YOLO检测 |

## 详细实施步骤

### 第一阶段：架构设计与分析

#### 1.1 模块架构分析
- [ ] 分析每个模块的流水线架构特点
- [ ] 识别共同的组件和接口
- [ ] 分析GPU内存使用模式
- [ ] 确定模块间的依赖关系

#### 1.2 GPU资源规划
- [ ] 评估每个模型的显存占用
- [ ] 设计GPU内存池分配策略
- [ ] 规划并行执行方案
- [ ] 确定队列大小和线程配置

#### 1.3 数据流设计
- [ ] 设计统一的数据结构
- [ ] 规划图像预处理流程
- [ ] 设计结果合并策略
- [ ] 确定JSON输出格式

### 第二阶段：综合检测程序开发

#### 2.1 创建comprehensive_det_onnx_gpu0.py
- [ ] 设计统一的数据结构类
- [ ] 实现模块管理器
- [ ] 创建综合流水线处理器
- [ ] 实现GPU资源管理

#### 2.2 模块集成策略
```python
# 核心组件设计
class ComprehensiveDetectionData:
    """统一的检测数据结构"""
    
class ModuleManager:
    """模块管理器 - 负责加载和管理所有检测模块"""
    
class ComprehensivePipelineProcessor:
    """综合流水线处理器 - 协调所有模块的并行执行"""
    
class ResultMerger:
    """结果合并器 - 合并所有模块的检测结果"""
```

#### 2.3 GPU内存管理
- [ ] 实现统一的GPU内存池
- [ ] 设计模型加载顺序
- [ ] 实现内存回收机制
- [ ] 监控GPU使用情况

#### 2.4 流水线并行设计
- [ ] 设计多模块并行执行策略
- [ ] 实现异步队列系统
- [ ] 协调不同模块的处理速度
- [ ] 实现负载均衡

### 第三阶段：结果处理与可视化

#### 3.1 结果合并策略
- [ ] 设计JSON合并格式
- [ ] 处理重叠检测结果
- [ ] 实现置信度排序
- [ ] 添加检测统计信息

#### 3.2 创建vis_comprehensive_det_onnx_gpu.py
- [ ] 实现多类别可视化
- [ ] 设计颜色编码方案
- [ ] 添加检测信息标注
- [ ] 生成可视化报告

#### 3.3 输出格式设计
```json
{
  "version": "comprehensive_v1.0",
  "image_info": {
    "filename": "image.jpg",
    "width": 1152,
    "height": 512
  },
  "detection_results": {
    "condition": [...],
    "hengwen": [...],
    "kaohong": [...],
    "guahui": [...],
    "zhousuo": [...],
    "chaohong": [...]
  },
  "statistics": {
    "total_detections": 25,
    "processing_time": 1.2,
    "module_times": {...}
  }
}
```

### 第四阶段：性能优化

#### 4.1 GPU资源优化
- [ ] 优化模型加载顺序
- [ ] 实现动态内存分配
- [ ] 减少GPU内存碎片
- [ ] 优化批处理大小

#### 4.2 流水线优化
- [ ] 调整线程池大小
- [ ] 优化队列缓冲区
- [ ] 实现智能调度
- [ ] 减少上下文切换

#### 4.3 性能监控
- [ ] 添加详细的性能统计
- [ ] 监控GPU利用率
- [ ] 分析瓶颈环节
- [ ] 生成性能报告

### 第五阶段：测试与验证

#### 5.1 功能测试
- [ ] 测试单模块功能
- [ ] 测试模块间协调
- [ ] 验证结果一致性
- [ ] 测试错误处理

#### 5.2 性能测试
- [ ] 测试处理速度
- [ ] 监控GPU使用
- [ ] 验证内存管理
- [ ] 测试并发性能

#### 5.3 集成测试
- [ ] 使用test_data/vis/中的5张图像测试
- [ ] 验证JSON输出格式
- [ ] 测试可视化功能
- [ ] 验证完整流程

## 技术实现要点

### GPU资源管理策略
1. **模型加载优化**：按显存占用大小排序加载
2. **内存池管理**：统一的GPU内存池，避免碎片
3. **批处理优化**：根据模型特点调整批处理大小
4. **动态调度**：根据GPU使用情况动态调整

### 并行执行策略
1. **流水线并行**：预处理/推理/后处理并行
2. **模块并行**：多个检测模块并行执行
3. **队列管理**：异步队列协调数据流
4. **负载均衡**：智能分配计算资源

### 结果合并策略
1. **分层合并**：按检测类型分层组织
2. **冲突处理**：处理重叠区域检测
3. **置信度排序**：按置信度排序结果
4. **统计信息**：添加详细的检测统计

## 预期性能目标

| 指标 | 目标值 | 备注 |
|------|--------|------|
| 处理速度 | >2张/秒 | 6个模块综合处理 |
| GPU利用率 | >80% | 充分利用24GB显存 |
| 内存效率 | <20GB | 预留4GB缓冲 |
| 并行效率 | >150% | 流水线+模块并行 |

## 风险评估与应对

### 主要风险
1. **GPU内存不足**：多模型同时加载可能超出24GB限制
2. **性能瓶颈**：某个模块可能成为整体瓶颈
3. **结果冲突**：不同模块可能产生冲突的检测结果
4. **系统稳定性**：复杂的并行系统可能不稳定

### 应对策略
1. **分批加载**：按需加载模型，及时释放内存
2. **动态调度**：根据性能动态调整资源分配
3. **智能合并**：设计合理的结果合并算法
4. **容错机制**：添加完善的错误处理和恢复机制

## 测试验证计划

### 测试数据
- **输入**：`/home/<USER>/xm/code/coderafactor/test_data/vis/` (5张图像)
- **输出**：`/home/<USER>/xm/code/coderafactor/test_data/test_output/`

### 验证命令
```bash
source ~/.bashrc && conda activate vllm && python comprehensive_det_onnx_gpu0.py
```

### 预期输出
1. **JSON文件**：每张图像对应一个综合检测结果JSON
2. **可视化图像**：每张图像对应一个可视化结果图像
3. **性能报告**：详细的处理时间和GPU使用统计
4. **检测统计**：各模块的检测数量和置信度分布

## 项目交付物

1. **comprehensive_det_onnx_gpu0.py**：综合检测主程序
2. **vis_comprehensive_det_onnx_gpu.py**：可视化程序
3. **测试结果**：5张图像的检测和可视化结果
4. **性能报告**：详细的性能分析报告
5. **使用文档**：程序使用说明和配置指南

---

**注意事项**：
- 严格按照步骤执行，确保每个阶段完成后再进入下一阶段
- 重点关注GPU内存管理，避免内存溢出
- 保持与原有模块的兼容性，不修改原有代码
- 充分测试验证，确保结果的准确性和一致性
