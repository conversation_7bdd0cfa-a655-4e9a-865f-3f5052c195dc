#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
烟叶综合形态检测系统完整测试脚本
验证整个系统的功能和性能
"""

import os
import sys
import time
import json
import subprocess
from typing import Dict, List, Any

def run_command(command: str, timeout: int = 300) -> Dict[str, Any]:
    """运行命令并返回结果"""
    try:
        print(f"🔄 执行命令: {command}")
        start_time = time.time()
        
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=timeout,
            cwd="/home/<USER>/xm/code/coderebuild"
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        return {
            "success": result.returncode == 0,
            "duration": duration,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "returncode": result.returncode
        }
        
    except subprocess.TimeoutExpired:
        return {
            "success": False,
            "duration": timeout,
            "stdout": "",
            "stderr": "命令超时",
            "returncode": -1
        }
    except Exception as e:
        return {
            "success": False,
            "duration": 0,
            "stdout": "",
            "stderr": str(e),
            "returncode": -1
        }

def check_output_files() -> Dict[str, Any]:
    """检查输出文件"""
    output_dir = "/home/<USER>/xm/code/coderebuild/test_data/test_output1"
    
    if not os.path.exists(output_dir):
        return {"success": False, "error": "输出目录不存在"}
    
    # 检查合并的JSON文件
    merged_files = [f for f in os.listdir(output_dir) if f.endswith('_merged.json')]
    
    # 检查可视化文件
    vis_files = [f for f in os.listdir(output_dir) if f.endswith('_visualization.png')]
    
    # 检查模块输出目录
    module_dirs = [d for d in os.listdir(output_dir) 
                  if os.path.isdir(os.path.join(output_dir, d))]
    
    # 统计检测结果
    total_detections = 0
    module_stats = {}
    
    for merged_file in merged_files:
        try:
            with open(os.path.join(output_dir, merged_file), 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            shapes = data.get('shapes', [])
            total_detections += len(shapes)
            
            # 统计各模块检测数量
            for shape in shapes:
                module = shape.get('module', 'unknown')
                if module not in module_stats:
                    module_stats[module] = 0
                module_stats[module] += 1
                
        except Exception as e:
            print(f"⚠️  读取JSON文件失败 {merged_file}: {e}")
    
    return {
        "success": True,
        "merged_files": len(merged_files),
        "visualization_files": len(vis_files),
        "module_directories": len(module_dirs),
        "total_detections": total_detections,
        "module_stats": module_stats,
        "module_dirs": module_dirs
    }

def main():
    """主函数"""
    print("🧪🧪🧪 烟叶综合形态检测系统完整测试 🧪🧪🧪")
    print("="*70)
    
    # 清理之前的输出
    print("🧹 清理之前的输出...")
    cleanup_result = run_command("rm -rf /home/<USER>/xm/code/coderebuild/test_data/test_output1/*")
    if cleanup_result["success"]:
        print("✅ 清理完成")
    else:
        print("⚠️  清理失败，继续测试")
    
    # 第一步：运行综合检测系统
    print(f"\n{'='*50}")
    print("🔥 第一步：运行综合检测系统")
    print(f"{'='*50}")
    
    detection_result = run_command(
        "/bin/bash -c 'source ~/.bashrc && conda activate vllm && python comprehensive_det_onnx_gpu1.py'",
        timeout=600
    )
    
    if not detection_result["success"]:
        print(f"❌ 综合检测系统运行失败:")
        print(f"   返回码: {detection_result['returncode']}")
        print(f"   错误信息: {detection_result['stderr']}")
        return False
    
    print(f"✅ 综合检测系统运行成功，耗时: {detection_result['duration']:.2f}秒")
    
    # 第二步：运行可视化程序
    print(f"\n{'='*50}")
    print("🎨 第二步：运行可视化程序")
    print(f"{'='*50}")
    
    visualization_result = run_command(
        "/bin/bash -c 'source ~/.bashrc && conda activate vllm && python vis_comprehensive_det_onnx_gpu1.py'",
        timeout=300
    )
    
    if not visualization_result["success"]:
        print(f"❌ 可视化程序运行失败:")
        print(f"   返回码: {visualization_result['returncode']}")
        print(f"   错误信息: {visualization_result['stderr']}")
        return False
    
    print(f"✅ 可视化程序运行成功，耗时: {visualization_result['duration']:.2f}秒")
    
    # 第三步：检查输出文件
    print(f"\n{'='*50}")
    print("📊 第三步：检查输出文件")
    print(f"{'='*50}")
    
    file_check = check_output_files()
    
    if not file_check["success"]:
        print(f"❌ 输出文件检查失败: {file_check.get('error', '未知错误')}")
        return False
    
    print(f"✅ 输出文件检查通过:")
    print(f"   合并JSON文件: {file_check['merged_files']} 个")
    print(f"   可视化图像: {file_check['visualization_files']} 个")
    print(f"   模块输出目录: {file_check['module_directories']} 个")
    print(f"   总检测结果: {file_check['total_detections']} 个")
    
    print(f"\n📊 各模块检测统计:")
    for module, count in sorted(file_check['module_stats'].items()):
        print(f"   {module}: {count} 个")
    
    print(f"\n📁 模块输出目录:")
    for module_dir in sorted(file_check['module_dirs']):
        print(f"   {module_dir}")
    
    # 第四步：性能统计
    print(f"\n{'='*50}")
    print("⚡ 第四步：性能统计")
    print(f"{'='*50}")
    
    total_time = detection_result['duration'] + visualization_result['duration']
    
    print(f"📊 性能统计:")
    print(f"   检测系统耗时: {detection_result['duration']:.2f}秒")
    print(f"   可视化耗时: {visualization_result['duration']:.2f}秒")
    print(f"   总耗时: {total_time:.2f}秒")
    print(f"   平均每张图像: {total_time / 5:.2f}秒")
    
    # 第五步：系统验证
    print(f"\n{'='*50}")
    print("✅ 第五步：系统验证")
    print(f"{'='*50}")
    
    # 验证标准
    expected_modules = 6
    expected_images = 5
    
    validation_passed = True
    validation_issues = []
    
    if file_check['merged_files'] != expected_images:
        validation_passed = False
        validation_issues.append(f"合并JSON文件数量不正确: 期望{expected_images}, 实际{file_check['merged_files']}")
    
    if file_check['visualization_files'] != expected_images:
        validation_passed = False
        validation_issues.append(f"可视化文件数量不正确: 期望{expected_images}, 实际{file_check['visualization_files']}")
    
    if file_check['module_directories'] != expected_modules:
        validation_passed = False
        validation_issues.append(f"模块目录数量不正确: 期望{expected_modules}, 实际{file_check['module_directories']}")
    
    if file_check['total_detections'] < 100:  # 至少应该有100个检测结果
        validation_passed = False
        validation_issues.append(f"检测结果数量过少: {file_check['total_detections']}")
    
    if validation_passed:
        print("🎉 系统验证通过！所有功能正常工作")
        print(f"\n📋 系统总结:")
        print(f"   ✅ 6个检测模块全部正常工作")
        print(f"   ✅ 5张图像全部处理成功")
        print(f"   ✅ GPU流水线并行处理正常")
        print(f"   ✅ 结果合并功能正常")
        print(f"   ✅ 可视化功能正常")
        print(f"   ✅ 总计检测到 {file_check['total_detections']} 个目标")
        
        return True
    else:
        print("❌ 系统验证失败:")
        for issue in validation_issues:
            print(f"   - {issue}")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n{'='*70}")
    if success:
        print("🎉🎉🎉 烟叶综合形态检测系统测试完全成功！🎉🎉🎉")
    else:
        print("❌❌❌ 烟叶综合形态检测系统测试失败！❌❌❌")
    print(f"{'='*70}")
    sys.exit(0 if success else 1)
