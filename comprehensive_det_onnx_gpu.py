#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
烟叶综合检测系统 - 双GPU流水线并行版本
整合所有12个检测模块：形态检测(6个) + 杂色检测(6个)

作者: 系统架构师
日期: 2025-01-30
版本: 2.0.0

集成模块：
形态检测模块(GPU1优先)：
1. 主脉打开检测 (zhumaidakai)
2. 主脉走势检测 (zhumaizoushi)
3. 折痕检测 (zheheng)
4. 支脉检测 (zhimai)
5. 支脉青检测 (zhimaiqing)
6. 轮廓残缺补全 (lunkuo_canque_fill)

杂色检测模块(GPU0优先)：
7. 焦点浮青等检测 (condition)
8. 横纹检测 (hengwen)
9. 烤红检测 (kaohong)
10. 挂灰检测 (guahui)
11. 皱缩检测 (zhousuo)
12. 潮红检测 (chaohong)

关键特性：
1. 双GPU智能资源管理
2. 模块化设计
3. 高效的并行处理
4. 智能结果合并
5. 完整的性能监控
"""

import os
import sys
import time
import json
import threading
import multiprocessing as mp
from typing import List, Dict, Optional, Any, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
import logging
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 添加coderafactor目录到Python路径
coderafactor_root = "/home/<USER>/xm/code/coderafactor"
if coderafactor_root not in sys.path:
    sys.path.insert(0, coderafactor_root)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

try:
    import torch
    import cv2
    import numpy as np
    import base_function as bf
    print("🔥🔥🔥 双GPU综合检测系统 - 成功导入所有基础模块 🔥🔥🔥")
except ImportError as e:
    print(f"❌ 错误: 无法导入基础模块 {e}")
    sys.exit(1)


@dataclass
class ComprehensiveDetectionConfig:
    """综合检测配置"""
    input_dir: str = "/home/<USER>/xm/code/coderafactor/test_data/vis"
    output_dir: str = "/home/<USER>/xm/code/coderafactor/test_data/test_outputs"
    gpu_primary: int = 1  # 主GPU - 形态检测优先
    gpu_secondary: int = 0  # 备用GPU - 杂色检测优先
    threshold: float = 0.5
    max_workers: int = 12  # 最大并行模块数
    enable_visualization: bool = True
    enable_performance_monitoring: bool = True
    
    def __post_init__(self):
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)


@dataclass
class ModuleInfo:
    """模块信息"""
    name: str
    module_path: str
    label_name: str
    memory_requirement: str  # 'high', 'medium', 'low'
    gpu_preference: int  # 优先GPU
    color: Tuple[int, int, int]  # 可视化颜色 (B, G, R)
    category: str  # 'morphology' 或 'discoloration'
    description: str = ""


class GPUResourceManager:
    """双GPU资源智能分配管理器"""
    
    def __init__(self, primary_gpu: int = 1, secondary_gpu: int = 0):
        self.primary_gpu = primary_gpu
        self.secondary_gpu = secondary_gpu
        self.gpu_memory_usage = {primary_gpu: 0.0, secondary_gpu: 0.0}
        self.gpu_lock = threading.Lock()
        self.morphology_modules = set()  # 形态检测模块
        self.discoloration_modules = set()  # 杂色检测模块
        self._check_gpu_availability()
    
    def _check_gpu_availability(self):
        """检查GPU可用性"""
        try:
            if torch.cuda.is_available():
                gpu_count = torch.cuda.device_count()
                print(f"🔧 检测到 {gpu_count} 个GPU设备")
                
                for i in range(gpu_count):
                    gpu_name = torch.cuda.get_device_name(i)
                    memory_total = torch.cuda.get_device_properties(i).total_memory / 1024**3
                    print(f"   GPU{i}: {gpu_name} ({memory_total:.1f}GB)")
                
                if self.primary_gpu >= gpu_count:
                    print(f"⚠️  主GPU{self.primary_gpu}不可用，使用GPU0")
                    self.primary_gpu = 0
                
                if self.secondary_gpu >= gpu_count:
                    print(f"⚠️  备用GPU{self.secondary_gpu}不可用，使用GPU0")
                    self.secondary_gpu = 0
            else:
                print("❌ 未检测到CUDA GPU，将使用CPU")
                self.primary_gpu = -1
                self.secondary_gpu = -1
        except Exception as e:
            print(f"❌ GPU检查失败: {e}")
            self.primary_gpu = -1
            self.secondary_gpu = -1
    
    def allocate_gpu(self, module_info: ModuleInfo) -> int:
        """为模块分配GPU"""
        with self.gpu_lock:
            if self.primary_gpu == -1:  # 无GPU可用
                return -1
            
            # 根据模块类别分配GPU
            if module_info.category == "morphology":
                # 形态检测模块优先分配到主GPU(GPU1)
                if module_info.name == "lunkuo_canque_fill":
                    # 轮廓残缺补全优先分配到主GPU
                    selected_gpu = self.primary_gpu
                    print(f"🎯 {module_info.name} (轮廓补全) 优先分配到GPU{selected_gpu}")
                else:
                    # 其他形态检测模块根据负载分配
                    primary_usage = self.gpu_memory_usage.get(self.primary_gpu, 0.0)
                    secondary_usage = self.gpu_memory_usage.get(self.secondary_gpu, 0.0)
                    
                    if primary_usage <= secondary_usage + 0.2:  # 给主GPU一些优先权
                        selected_gpu = self.primary_gpu
                    else:
                        selected_gpu = self.secondary_gpu
                    
                    print(f"🎯 {module_info.name} (形态检测) 分配到GPU{selected_gpu}")
                
                self.morphology_modules.add(module_info.name)
                
            else:  # discoloration
                # 杂色检测模块优先分配到备用GPU(GPU0)
                primary_usage = self.gpu_memory_usage.get(self.primary_gpu, 0.0)
                secondary_usage = self.gpu_memory_usage.get(self.secondary_gpu, 0.0)
                
                if secondary_usage <= primary_usage + 0.2:  # 给备用GPU一些优先权
                    selected_gpu = self.secondary_gpu
                else:
                    selected_gpu = self.primary_gpu
                
                print(f"🎯 {module_info.name} (杂色检测) 分配到GPU{selected_gpu}")
                self.discoloration_modules.add(module_info.name)
            
            # 更新显存使用估计
            memory_increment = 0.3 if module_info.memory_requirement == 'high' else 0.2
            self.gpu_memory_usage[selected_gpu] += memory_increment
            
            return selected_gpu
    
    def release_gpu(self, gpu_id: int, module_info: ModuleInfo):
        """释放GPU资源"""
        with self.gpu_lock:
            if gpu_id in self.gpu_memory_usage:
                memory_decrement = 0.3 if module_info.memory_requirement == 'high' else 0.2
                self.gpu_memory_usage[gpu_id] = max(0.0, self.gpu_memory_usage[gpu_id] - memory_decrement)
                print(f"🔄 释放GPU{gpu_id}资源 ({module_info.name})")
                
                # 从模块集合中移除
                self.morphology_modules.discard(module_info.name)
                self.discoloration_modules.discard(module_info.name)
    
    def get_gpu_status(self) -> Dict[str, Any]:
        """获取GPU状态"""
        return {
            "gpu_memory_usage": self.gpu_memory_usage.copy(),
            "morphology_modules": list(self.morphology_modules),
            "discoloration_modules": list(self.discoloration_modules),
            "total_modules": len(self.morphology_modules) + len(self.discoloration_modules)
        }


class DetectionModuleWrapper:
    """检测模块统一包装器"""
    
    def __init__(self, module_info: ModuleInfo, gpu_manager: GPUResourceManager):
        self.module_info = module_info
        self.gpu_manager = gpu_manager
        self.module = None
        self.gpu_id = -1
        self.is_loaded = False
    
    def load_module(self) -> bool:
        """加载检测模块"""
        try:
            print(f"🔄 加载模块: {self.module_info.name} ({self.module_info.category})")
            
            # 分配GPU
            self.gpu_id = self.gpu_manager.allocate_gpu(self.module_info)
            
            # 动态导入模块
            module_path = self.module_info.module_path
            if os.path.exists(module_path):
                # 添加模块目录到路径
                module_dir = os.path.dirname(module_path)
                if module_dir not in sys.path:
                    sys.path.insert(0, module_dir)
                
                # 导入模块
                module_name = os.path.basename(module_path).replace('.py', '')
                self.module = __import__(module_name)
                
                print(f"✅ 成功加载模块: {self.module_info.name}")
                self.is_loaded = True
                return True
            else:
                print(f"❌ 模块文件不存在: {module_path}")
                return False
                
        except Exception as e:
            print(f"❌ 加载模块失败 {self.module_info.name}: {e}")
            return False
    
    def process_images(self, image_paths: List[str], output_dir: str, 
                      threshold: float = 0.5) -> Dict[str, Any]:
        """处理图像列表"""
        if not self.is_loaded:
            return {"success": False, "error": "模块未加载"}
        
        try:
            print(f"🔄 {self.module_info.name} 开始处理 {len(image_paths)} 张图像")
            start_time = time.time()
            
            # 创建模块专用输出目录
            module_output_dir = os.path.join(output_dir, self.module_info.name)
            os.makedirs(module_output_dir, exist_ok=True)
            
            # 调用模块的处理函数
            if hasattr(self.module, 'run_det_gpu_final'):
                # 使用GPU处理函数
                success_count = self.module.run_det_gpu_final(
                    input_image_dir=os.path.dirname(image_paths[0]),
                    output_json_dir=module_output_dir,
                    threshold=threshold,
                    label_name=self.module_info.label_name
                )
            elif hasattr(self.module, 'main'):
                # 使用main函数 - 需要修改工作目录
                original_cwd = os.getcwd()
                try:
                    # 切换到模块目录
                    module_dir = os.path.dirname(self.module_info.module_path)
                    os.chdir(module_dir)
                    success_count = len(image_paths)  # 假设全部成功
                    self.module.main()
                finally:
                    # 恢复原工作目录
                    os.chdir(original_cwd)
            else:
                print(f"❌ 模块 {self.module_info.name} 没有可用的处理函数")
                return {"success": False, "error": "无可用处理函数"}
            
            processing_time = time.time() - start_time
            
            # 检查输出文件并复制到目标目录
            actual_output_dir = self._find_and_copy_outputs(module_output_dir)
            
            print(f"✅ {self.module_info.name} 处理完成: {success_count}/{len(image_paths)} 张图像, 耗时: {processing_time:.2f}秒")
            
            return {
                "success": True,
                "processed_count": success_count,
                "total_count": len(image_paths),
                "processing_time": processing_time,
                "output_dir": actual_output_dir
            }
            
        except Exception as e:
            print(f"❌ {self.module_info.name} 处理失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _find_and_copy_outputs(self, target_dir: str) -> str:
        """查找模块输出文件并复制到目标目录"""
        try:
            # 模块可能的输出目录
            module_dir = os.path.dirname(self.module_info.module_path)
            possible_output_dirs = [
                os.path.join(module_dir, "test_output_gpu_final"),
                os.path.join(module_dir, "test_output_gpu_pipeline_v2"),
                os.path.join(module_dir, "test_output"),
                os.path.join(module_dir, "output")
            ]
            
            copied_files = 0
            for output_dir in possible_output_dirs:
                if os.path.exists(output_dir):
                    # 复制JSON文件
                    for file in os.listdir(output_dir):
                        if file.endswith('.json'):
                            src_path = os.path.join(output_dir, file)
                            dst_path = os.path.join(target_dir, file)
                            
                            # 复制文件
                            import shutil
                            shutil.copy2(src_path, dst_path)
                            copied_files += 1
                            print(f"📋 复制输出文件: {file}")
            
            if copied_files > 0:
                print(f"✅ 成功复制 {copied_files} 个输出文件到 {target_dir}")
            else:
                print(f"⚠️  未找到输出文件，检查目录: {possible_output_dirs}")
            
            return target_dir
            
        except Exception as e:
            print(f"❌ 复制输出文件失败: {e}")
            return target_dir
    
    def unload_module(self):
        """卸载模块并释放资源"""
        if self.gpu_id != -1:
            self.gpu_manager.release_gpu(self.gpu_id, self.module_info)
        self.is_loaded = False
        print(f"🔄 卸载模块: {self.module_info.name}")


def get_all_detection_modules() -> List[ModuleInfo]:
    """获取所有检测模块信息"""
    base_path = "/home/<USER>/xm/code/coderebuild"
    
    modules = []
    
    # 形态检测模块 (GPU1优先)
    morphology_modules = [
        ModuleInfo(
            name="zhumaidakai",
            module_path=f"{base_path}/seg_det_zhumaidakai/deploy_seg_det_zhumaidakai_onnx_gpu_final.py",
            label_name="zhumaidakai",
            memory_requirement="medium",
            gpu_preference=1,
            color=(0, 0, 255),  # 红色
            category="morphology",
            description="主脉打开检测"
        ),
        ModuleInfo(
            name="zhumaizoushi", 
            module_path=f"{base_path}/seg_det_zhumaizoushi/deploy_seg_det_zhumaizoushi_onnx_gpu_final.py",
            label_name="zhumaizoushi",
            memory_requirement="medium",
            gpu_preference=1,
            color=(255, 0, 0),  # 蓝色
            category="morphology",
            description="主脉走势检测"
        ),
        ModuleInfo(
            name="zheheng",
            module_path=f"{base_path}/seg_det_zheheng/deploy_seg_det_zheheng_onnx_gpu_final.py", 
            label_name="zheheng",
            memory_requirement="medium",
            gpu_preference=1,
            color=(0, 255, 0),  # 绿色
            category="morphology",
            description="折痕检测"
        ),
        ModuleInfo(
            name="zhimai",
            module_path=f"{base_path}/seg_det_zhimai/deploy_seg_det_zhimai_onnx_gpu_final.py",
            label_name="zhimai", 
            memory_requirement="medium",
            gpu_preference=1,
            color=(0, 255, 255),  # 黄色
            category="morphology",
            description="支脉检测"
        ),
        ModuleInfo(
            name="zhimaiqing",
            module_path=f"{base_path}/seg_det_zhimaiqing/deploy_seg_det_zhimaiqing_onnx_gpu_final.py",
            label_name="zhimaiqing",
            memory_requirement="medium", 
            gpu_preference=1,
            color=(255, 255, 0),  # 青色
            category="morphology",
            description="支脉青检测"
        ),
        ModuleInfo(
            name="lunkuo_canque_fill",
            module_path=f"{base_path}/seg_det_lunkuo_canque_fill/deploy_seg_lunkuo_canque_fill_onnx_gpu_final.py",
            label_name="lunkuo_canque_fill",
            memory_requirement="high",  # 显存占用大
            gpu_preference=1,  # 优先GPU1
            color=(128, 0, 128),  # 紫色
            category="morphology",
            description="轮廓残缺补全"
        )
    ]
    
    modules.extend(morphology_modules)

    # 杂色检测模块 (GPU0优先)
    discoloration_modules = [
        ModuleInfo(
            name="condition",
            module_path=f"{base_path}/seg_det_jiaodian/deploy_seg_det_condition_onnx_gpu_final.py",
            label_name="condition",
            memory_requirement="medium",
            gpu_preference=0,
            color=(255, 128, 0),  # 橙色
            category="discoloration",
            description="焦点浮青等检测"
        ),
        ModuleInfo(
            name="hengwen",
            module_path=f"{base_path}/seg_det_hengwen/deploy_seg_det_hengwen_onnx_gpu_final.py",
            label_name="hengwen",
            memory_requirement="medium",
            gpu_preference=0,
            color=(128, 255, 0),  # 黄绿色
            category="discoloration",
            description="横纹检测"
        ),
        ModuleInfo(
            name="kaohong",
            module_path=f"{base_path}/seg_det_kaohong/deploy_seg_det_kaohong_onnx_gpu_final.py",
            label_name="kaohong",
            memory_requirement="medium",
            gpu_preference=0,
            color=(255, 0, 128),  # 粉红色
            category="discoloration",
            description="烤红检测"
        ),
        ModuleInfo(
            name="guahui",
            module_path=f"{base_path}/seg_det_kaohong/deploy_seg_det_guahui_onnx_gpu_final.py",
            label_name="guahui",
            memory_requirement="medium",
            gpu_preference=0,
            color=(128, 128, 255),  # 浅蓝色
            category="discoloration",
            description="挂灰检测"
        ),
        ModuleInfo(
            name="zhousuo",
            module_path=f"{base_path}/seg_det_zhousuo/deploy_seg_det_zhousuo_onnx_gpu_final.py",
            label_name="zhousuo",
            memory_requirement="medium",
            gpu_preference=0,
            color=(255, 128, 128),  # 浅红色
            category="discoloration",
            description="皱缩检测"
        ),
        ModuleInfo(
            name="chaohong",
            module_path=f"{base_path}/seg_det_chaohong/deploy_seg_det_chaohong_onnx_gpu_final.py",
            label_name="chaohong",
            memory_requirement="medium",
            gpu_preference=0,
            color=(128, 255, 255),  # 浅青色
            category="discoloration",
            description="潮红检测"
        )
    ]

    modules.extend(discoloration_modules)

    print(f"🔧 配置完成: {len(morphology_modules)} 个形态检测模块 + {len(discoloration_modules)} 个杂色检测模块")

    return modules


class PipelineScheduler:
    """流水线并行调度器"""

    def __init__(self, config: ComprehensiveDetectionConfig):
        self.config = config
        self.gpu_manager = GPUResourceManager(config.gpu_primary, config.gpu_secondary)

    def run_parallel_detection(self, modules: List[ModuleInfo], image_paths: List[str]) -> Dict[str, Any]:
        """并行运行所有检测模块"""
        print(f"\n🚀 双GPU并行检测处理")
        print("="*60)
        print(f"🔧 使用 {len(modules)} 个模块并行处理 {len(image_paths)} 张图像")

        # 按类别分组显示
        morphology_count = len([m for m in modules if m.category == "morphology"])
        discoloration_count = len([m for m in modules if m.category == "discoloration"])
        print(f"📊 形态检测模块: {morphology_count} 个 (GPU{self.config.gpu_primary}优先)")
        print(f"📊 杂色检测模块: {discoloration_count} 个 (GPU{self.config.gpu_secondary}优先)")

        start_time = time.time()
        results = {}

        # 使用线程池并行处理
        with ThreadPoolExecutor(max_workers=self.config.max_workers) as executor:
            # 提交所有模块任务
            future_to_module = {}
            for module_info in modules:
                future = executor.submit(self._process_module, module_info, image_paths)
                future_to_module[future] = module_info

            # 收集结果
            completed_count = 0
            for future in as_completed(future_to_module):
                module_info = future_to_module[future]
                completed_count += 1

                try:
                    result = future.result(timeout=300)  # 5分钟超时
                    results[module_info.name] = result

                    if result["success"]:
                        print(f"✅ [{completed_count:2d}/{len(modules)}] {module_info.name} ({module_info.category}) 完成: {result['processed_count']} 张图像, 耗时: {result['processing_time']:.2f}秒")
                    else:
                        print(f"❌ [{completed_count:2d}/{len(modules)}] {module_info.name} ({module_info.category}) 失败: {result.get('error', '未知错误')}")

                except Exception as e:
                    print(f"❌ [{completed_count:2d}/{len(modules)}] {module_info.name} ({module_info.category}) 异常: {e}")
                    results[module_info.name] = {"success": False, "error": str(e)}

        total_time = time.time() - start_time

        # 统计结果
        successful_modules = [name for name, result in results.items() if result["success"]]
        failed_modules = [name for name, result in results.items() if not result["success"]]

        # 按类别统计
        successful_morphology = [name for name in successful_modules
                               if any(m.name == name and m.category == "morphology" for m in modules)]
        successful_discoloration = [name for name in successful_modules
                                  if any(m.name == name and m.category == "discoloration" for m in modules)]

        print(f"\n📊 并行处理结果:")
        print(f"✅ 总成功: {len(successful_modules)}/{len(modules)} 个模块")
        print(f"   形态检测: {len(successful_morphology)} 个")
        print(f"   杂色检测: {len(successful_discoloration)} 个")
        print(f"❌ 失败: {len(failed_modules)} 个模块")
        print(f"⏱️  总耗时: {total_time:.2f}秒")

        if failed_modules:
            print("失败的模块:")
            for module_name in failed_modules:
                error_msg = results[module_name].get('error', '未知错误')
                print(f"   - {module_name}: {error_msg}")

        # 显示GPU使用情况
        gpu_status = self.gpu_manager.get_gpu_status()
        print(f"\n🔧 GPU使用统计:")
        print(f"   GPU{self.config.gpu_primary} (主): {len(gpu_status['morphology_modules'])} 个形态检测模块")
        print(f"   GPU{self.config.gpu_secondary} (备): {len(gpu_status['discoloration_modules'])} 个杂色检测模块")

        return {
            "success": len(successful_modules) > 0,
            "total_time": total_time,
            "successful_modules": successful_modules,
            "failed_modules": failed_modules,
            "results": results,
            "gpu_status": gpu_status
        }

    def _process_module(self, module_info: ModuleInfo, image_paths: List[str]) -> Dict[str, Any]:
        """处理单个模块"""
        wrapper = DetectionModuleWrapper(module_info, self.gpu_manager)

        try:
            # 加载模块
            if not wrapper.load_module():
                return {"success": False, "error": "模块加载失败"}

            # 处理图像
            result = wrapper.process_images(image_paths, self.config.output_dir, self.config.threshold)
            return result

        finally:
            # 清理资源
            wrapper.unload_module()

    def merge_detection_results(self, image_paths: List[str], successful_modules: List[str],
                              all_modules: List[ModuleInfo]) -> bool:
        """合并检测结果"""
        print(f"\n🔄 合并检测结果")
        print("="*50)

        try:
            for image_path in image_paths:
                image_name = os.path.basename(image_path)
                base_name = os.path.splitext(image_name)[0]

                # 创建合并的JSON结构
                merged_result = {
                    "version": "4.5.6",
                    "flags": {},
                    "shapes": [],
                    "imagePath": image_name,
                    "imageData": None,
                    "imageHeight": 0,
                    "imageWidth": 0,
                    "detection_info": {
                        "modules": successful_modules,
                        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                        "total_detections": 0,
                        "morphology_detections": 0,
                        "discoloration_detections": 0,
                        "module_categories": {}
                    }
                }

                total_detections = 0
                morphology_detections = 0
                discoloration_detections = 0

                # 合并各模块的检测结果
                for module_name in successful_modules:
                    module_json_path = os.path.join(
                        self.config.output_dir, module_name, f"{base_name}.json"
                    )

                    # 获取模块信息
                    module_info = next((m for m in all_modules if m.name == module_name), None)

                    if os.path.exists(module_json_path):
                        try:
                            with open(module_json_path, 'r', encoding='utf-8') as f:
                                module_data = json.load(f)

                            # 添加模块的检测结果
                            if "shapes" in module_data:
                                module_detection_count = 0
                                for shape in module_data["shapes"]:
                                    # 添加模块信息到shape
                                    shape["module"] = module_name
                                    if module_info:
                                        shape["category"] = module_info.category
                                        shape["description"] = module_info.description
                                    merged_result["shapes"].append(shape)
                                    total_detections += 1
                                    module_detection_count += 1

                                    # 按类别统计
                                    if module_info and module_info.category == "morphology":
                                        morphology_detections += 1
                                    elif module_info and module_info.category == "discoloration":
                                        discoloration_detections += 1

                                # 记录各模块的检测数量
                                merged_result["detection_info"]["module_categories"][module_name] = {
                                    "count": module_detection_count,
                                    "category": module_info.category if module_info else "unknown"
                                }

                            # 更新图像尺寸信息
                            if "imageHeight" in module_data and module_data["imageHeight"] > 0:
                                merged_result["imageHeight"] = module_data["imageHeight"]
                                merged_result["imageWidth"] = module_data["imageWidth"]

                        except Exception as e:
                            print(f"⚠️  读取模块结果失败 {module_name}/{base_name}.json: {e}")
                    else:
                        print(f"⚠️  模块结果文件不存在: {module_json_path}")

                # 更新统计信息
                merged_result["detection_info"]["total_detections"] = total_detections
                merged_result["detection_info"]["morphology_detections"] = morphology_detections
                merged_result["detection_info"]["discoloration_detections"] = discoloration_detections

                # 保存合并结果
                merged_json_path = os.path.join(self.config.output_dir, f"{base_name}_comprehensive.json")
                with open(merged_json_path, 'w', encoding='utf-8') as f:
                    json.dump(merged_result, f, ensure_ascii=False, indent=2)

                print(f"✅ 合并完成: {base_name}_comprehensive.json")
                print(f"   总检测: {total_detections} 个 (形态: {morphology_detections}, 杂色: {discoloration_detections})")

            print(f"🎉 所有检测结果合并完成！")
            return True

        except Exception as e:
            print(f"❌ 合并检测结果失败: {e}")
            return False


def test_single_module(module_info: ModuleInfo, config: ComprehensiveDetectionConfig) -> bool:
    """测试单个模块是否能正常生成JSON"""
    print(f"\n🧪 测试模块: {module_info.name} ({module_info.category})")
    print("-" * 40)

    try:
        # 创建GPU管理器
        gpu_manager = GPUResourceManager(config.gpu_primary, config.gpu_secondary)

        # 创建模块包装器
        wrapper = DetectionModuleWrapper(module_info, gpu_manager)

        # 加载模块
        if not wrapper.load_module():
            print(f"❌ 模块 {module_info.name} 加载失败")
            return False

        # 获取测试图像
        image_files = [f for f in os.listdir(config.input_dir) if f.endswith('.bmp')]
        if not image_files:
            print(f"❌ 测试目录中没有找到图像文件")
            return False

        # 只测试第一张图像
        test_image = os.path.join(config.input_dir, image_files[0])

        # 处理图像
        result = wrapper.process_images([test_image], config.output_dir, config.threshold)

        # 检查结果
        if result["success"]:
            # 检查是否生成了JSON文件
            module_output_dir = result["output_dir"]
            json_files = [f for f in os.listdir(module_output_dir) if f.endswith('.json')]

            if json_files:
                print(f"✅ 模块 {module_info.name} 测试成功，生成了 {len(json_files)} 个JSON文件")

                # 验证JSON格式
                test_json_path = os.path.join(module_output_dir, json_files[0])
                try:
                    with open(test_json_path, 'r', encoding='utf-8') as f:
                        json_data = json.load(f)
                    print(f"✅ JSON格式验证通过 (包含 {len(json_data.get('shapes', []))} 个检测结果)")
                    return True
                except Exception as e:
                    print(f"❌ JSON格式验证失败: {e}")
                    return False
            else:
                print(f"❌ 模块 {module_info.name} 没有生成JSON文件")
                return False
        else:
            print(f"❌ 模块 {module_info.name} 处理失败: {result.get('error', '未知错误')}")
            return False

    except Exception as e:
        print(f"❌ 测试模块 {module_info.name} 时发生异常: {e}")
        return False
    finally:
        # 清理资源
        if 'wrapper' in locals():
            wrapper.unload_module()


def main():
    """主函数"""
    print("🔥🔥🔥 烟叶综合检测系统 - 双GPU流水线并行版本 🔥🔥🔥")
    print("="*80)
    print("集成12个检测模块：6个形态检测 + 6个杂色检测")
    print("="*80)

    # 创建配置
    config = ComprehensiveDetectionConfig()

    # 验证输入输出目录
    if not os.path.exists(config.input_dir):
        print(f"❌ 输入目录不存在: {config.input_dir}")
        return False

    os.makedirs(config.output_dir, exist_ok=True)
    print(f"📁 输入目录: {config.input_dir}")
    print(f"📁 输出目录: {config.output_dir}")

    # 获取所有检测模块
    modules = get_all_detection_modules()
    print(f"🔧 检测到 {len(modules)} 个检测模块")

    # 获取图像文件列表
    image_files = [f for f in os.listdir(config.input_dir) if f.endswith('.bmp')]
    image_paths = [os.path.join(config.input_dir, f) for f in image_files]
    print(f"📷 找到 {len(image_paths)} 张图像")

    if not image_paths:
        print("❌ 没有找到图像文件")
        return False

    # 第一阶段：测试每个模块
    print(f"\n🧪 第一阶段：测试各个模块")
    print("="*60)

    successful_modules = []
    failed_modules = []

    for module_info in modules:
        if test_single_module(module_info, config):
            successful_modules.append(module_info)
        else:
            failed_modules.append(module_info)

    # 报告测试结果
    print(f"\n📊 模块测试结果:")
    print(f"✅ 成功: {len(successful_modules)} 个模块")
    print(f"❌ 失败: {len(failed_modules)} 个模块")

    # 按类别统计
    successful_morphology = [m for m in successful_modules if m.category == "morphology"]
    successful_discoloration = [m for m in successful_modules if m.category == "discoloration"]

    print(f"   形态检测: {len(successful_morphology)} 个")
    print(f"   杂色检测: {len(successful_discoloration)} 个")

    if failed_modules:
        print("失败的模块:")
        for module in failed_modules:
            print(f"   - {module.name} ({module.category})")

    if not successful_modules:
        print("❌ 没有可用的检测模块，程序退出")
        return False

    print(f"\n✅ 第一阶段测试完成，{len(successful_modules)} 个模块可用")

    # 第二阶段：并行处理
    scheduler = PipelineScheduler(config)
    parallel_result = scheduler.run_parallel_detection(successful_modules, image_paths)

    if not parallel_result["success"]:
        print("❌ 并行处理失败")
        return False

    # 第三阶段：合并结果
    merge_success = scheduler.merge_detection_results(
        image_paths, parallel_result["successful_modules"], successful_modules
    )

    if not merge_success:
        print("❌ 结果合并失败")
        return False

    print(f"\n🎉 烟叶综合检测系统处理完成！")
    print(f"📊 处理统计:")
    print(f"   图像数量: {len(image_paths)}")
    print(f"   成功模块: {len(parallel_result['successful_modules'])}")
    print(f"   总耗时: {parallel_result['total_time']:.2f}秒")
    print(f"   平均每张图像: {parallel_result['total_time'] / len(image_paths):.2f}秒")
    print(f"   输出目录: {config.output_dir}")

    # GPU使用统计
    gpu_status = parallel_result["gpu_status"]
    print(f"\n🔧 GPU使用统计:")
    print(f"   GPU{config.gpu_primary} (主): {len(gpu_status['morphology_modules'])} 个形态检测模块")
    print(f"   GPU{config.gpu_secondary} (备): {len(gpu_status['discoloration_modules'])} 个杂色检测模块")

    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
