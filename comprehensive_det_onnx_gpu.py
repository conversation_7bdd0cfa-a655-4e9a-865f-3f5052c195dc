#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
烟叶综合检测系统 - 双GPU流水线并行版本
整合所有12个检测模块：形态检测(6个) + 杂色检测(6个)

作者: 系统架构师
日期: 2025-01-30
版本: 2.0.0

集成模块：
形态检测模块(GPU1优先)：
1. 主脉打开检测 (zhumaidakai)
2. 主脉走势检测 (zhumaizoushi)
3. 折痕检测 (zheheng)
4. 支脉检测 (zhimai)
5. 支脉青检测 (zhimaiqing)
6. 轮廓残缺补全 (lunkuo_canque_fill)

杂色检测模块(GPU0优先)：
7. 焦点浮青等检测 (condition)
8. 横纹检测 (hengwen)
9. 烤红检测 (kaohong)
10. 挂灰检测 (guahui)
11. 皱缩检测 (zhousuo)
12. 潮红检测 (chaohong)

关键特性：
1. 双GPU智能资源管理
2. 模块化设计
3. 高效的并行处理
4. 智能结果合并
5. 完整的性能监控
"""

import os
import sys
import time
import json
import threading
import multiprocessing as mp
from typing import List, Dict, Optional, Any, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
import logging
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 添加coderafactor目录到Python路径
coderafactor_root = "/home/<USER>/xm/code/coderafactor"
if coderafactor_root not in sys.path:
    sys.path.insert(0, coderafactor_root)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

try:
    import torch
    import cv2
    import numpy as np
    import base_function as bf
    print("🔥🔥🔥 双GPU综合检测系统 - 成功导入所有基础模块 🔥🔥🔥")
except ImportError as e:
    print(f"❌ 错误: 无法导入基础模块 {e}")
    sys.exit(1)


@dataclass
class ComprehensiveDetectionConfig:
    """综合检测配置 - 高性能版本"""
    input_dir: str = "/home/<USER>/xm/code/coderafactor/test_data/vis"
    output_dir: str = "/home/<USER>/xm/code/coderafactor/test_data/test_outputs"
    gpu_primary: int = 0  # 主GPU - GPU0算力更强，承担更多负载
    gpu_secondary: int = 1  # 备用GPU - GPU1
    threshold: float = 0.5
    max_workers: int = 24  # 大幅增加并行模块数，充分利用GPU
    enable_visualization: bool = True
    enable_performance_monitoring: bool = True

    # 高性能配置
    enable_aggressive_gpu_usage: bool = True  # 激进GPU使用策略
    gpu0_max_modules: int = 8  # GPU0最大模块数（算力更强）
    gpu1_max_modules: int = 6  # GPU1最大模块数
    enable_batch_processing: bool = True  # 启用批处理
    batch_size: int = 5  # 批处理大小（所有图像一次性处理）
    enable_memory_optimization: bool = True  # 启用内存优化

    def __post_init__(self):
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)

        # 根据CPU核心数调整最大工作线程
        cpu_count = mp.cpu_count()
        if cpu_count >= 96:  # 高性能服务器
            self.max_workers = min(32, cpu_count // 3)
        elif cpu_count >= 48:
            self.max_workers = min(24, cpu_count // 2)
        else:
            self.max_workers = min(16, cpu_count)

        print(f"🚀 高性能配置: 最大工作线程={self.max_workers}, GPU0最大模块={self.gpu0_max_modules}, GPU1最大模块={self.gpu1_max_modules}")


@dataclass
class ModuleInfo:
    """模块信息"""
    name: str
    module_path: str
    label_name: str
    memory_requirement: str  # 'high', 'medium', 'low'
    gpu_preference: int  # 优先GPU
    color: Tuple[int, int, int]  # 可视化颜色 (B, G, R)
    category: str  # 'morphology' 或 'discoloration'
    description: str = ""


class GPUResourceManager:
    """双GPU资源智能分配管理器 - 高性能版本"""

    def __init__(self, primary_gpu: int = 0, secondary_gpu: int = 1, config: ComprehensiveDetectionConfig = None):
        self.primary_gpu = primary_gpu  # GPU0算力更强
        self.secondary_gpu = secondary_gpu  # GPU1
        self.config = config or ComprehensiveDetectionConfig()

        # 高性能内存管理
        self.gpu_memory_usage = {primary_gpu: 0.0, secondary_gpu: 0.0}
        self.gpu_module_count = {primary_gpu: 0, secondary_gpu: 0}
        self.gpu_lock = threading.Lock()

        # 模块分配跟踪
        self.gpu0_modules = set()  # GPU0上的模块
        self.gpu1_modules = set()  # GPU1上的模块

        # GPU性能参数
        self.gpu_capabilities = {}
        self._check_gpu_availability()
        self._initialize_gpu_capabilities()
    
    def _check_gpu_availability(self):
        """检查GPU可用性"""
        try:
            if torch.cuda.is_available():
                gpu_count = torch.cuda.device_count()
                print(f"🔧 检测到 {gpu_count} 个GPU设备")

                total_memory = 0
                for i in range(gpu_count):
                    gpu_name = torch.cuda.get_device_name(i)
                    memory_total = torch.cuda.get_device_properties(i).total_memory / 1024**3
                    memory_free = torch.cuda.get_device_properties(i).total_memory / 1024**3  # 简化计算
                    compute_capability = torch.cuda.get_device_properties(i).major

                    print(f"   GPU{i}: {gpu_name} ({memory_total:.1f}GB, 计算能力: {compute_capability}.x)")
                    total_memory += memory_total

                    # 存储GPU性能信息
                    self.gpu_capabilities[i] = {
                        "name": gpu_name,
                        "memory_total": memory_total,
                        "memory_free": memory_free,
                        "compute_capability": compute_capability,
                        "performance_score": compute_capability * memory_total  # 简单的性能评分
                    }

                print(f"🚀 总显存: {total_memory:.1f}GB，将充分利用所有资源")

                if self.primary_gpu >= gpu_count:
                    print(f"⚠️  主GPU{self.primary_gpu}不可用，使用GPU0")
                    self.primary_gpu = 0

                if self.secondary_gpu >= gpu_count:
                    print(f"⚠️  备用GPU{self.secondary_gpu}不可用，使用GPU1")
                    self.secondary_gpu = 1 if gpu_count > 1 else 0
            else:
                print("❌ 未检测到CUDA GPU，将使用CPU")
                self.primary_gpu = -1
                self.secondary_gpu = -1
        except Exception as e:
            print(f"❌ GPU检查失败: {e}")
            self.primary_gpu = -1
            self.secondary_gpu = -1

    def _initialize_gpu_capabilities(self):
        """初始化GPU性能参数"""
        if self.primary_gpu != -1 and self.secondary_gpu != -1:
            gpu0_score = self.gpu_capabilities.get(self.primary_gpu, {}).get("performance_score", 0)
            gpu1_score = self.gpu_capabilities.get(self.secondary_gpu, {}).get("performance_score", 0)

            print(f"🎯 GPU性能评分: GPU{self.primary_gpu}={gpu0_score:.1f}, GPU{self.secondary_gpu}={gpu1_score:.1f}")

            # 如果GPU0性能更强，给它分配更多模块
            if gpu0_score > gpu1_score:
                print(f"🚀 GPU{self.primary_gpu}性能更强，将承担更多计算负载")
    
    def allocate_gpu(self, module_info: ModuleInfo) -> int:
        """为模块分配GPU - 高性能策略"""
        with self.gpu_lock:
            if self.primary_gpu == -1:  # 无GPU可用
                return -1

            # 获取当前GPU负载
            gpu0_count = self.gpu_module_count.get(self.primary_gpu, 0)
            gpu1_count = self.gpu_module_count.get(self.secondary_gpu, 0)
            gpu0_memory = self.gpu_memory_usage.get(self.primary_gpu, 0.0)
            gpu1_memory = self.gpu_memory_usage.get(self.secondary_gpu, 0.0)

            # 激进分配策略：优先使用GPU0（算力更强）
            if self.config.enable_aggressive_gpu_usage:
                # GPU0优先策略 - 充分利用强GPU
                if gpu0_count < self.config.gpu0_max_modules and gpu0_memory < 0.85:
                    selected_gpu = self.primary_gpu
                    allocation_reason = "GPU0优先(算力强)"
                elif gpu1_count < self.config.gpu1_max_modules and gpu1_memory < 0.85:
                    selected_gpu = self.secondary_gpu
                    allocation_reason = "GPU1备用"
                else:
                    # 强制分配到负载较轻的GPU
                    if gpu0_memory <= gpu1_memory:
                        selected_gpu = self.primary_gpu
                        allocation_reason = "GPU0强制分配"
                    else:
                        selected_gpu = self.secondary_gpu
                        allocation_reason = "GPU1强制分配"
            else:
                # 传统负载均衡策略
                if module_info.category == "morphology":
                    # 形态检测模块
                    if module_info.name == "lunkuo_canque_fill":
                        # 轮廓残缺补全优先分配到GPU0（算力强）
                        selected_gpu = self.primary_gpu
                        allocation_reason = "轮廓补全(高负载)"
                    else:
                        # 负载均衡
                        if gpu0_count <= gpu1_count:
                            selected_gpu = self.primary_gpu
                            allocation_reason = "形态检测(负载均衡)"
                        else:
                            selected_gpu = self.secondary_gpu
                            allocation_reason = "形态检测(负载均衡)"
                else:  # discoloration
                    # 杂色检测模块 - 优先分配到GPU0
                    if gpu0_count < self.config.gpu0_max_modules:
                        selected_gpu = self.primary_gpu
                        allocation_reason = "杂色检测(GPU0优先)"
                    else:
                        selected_gpu = self.secondary_gpu
                        allocation_reason = "杂色检测(GPU1备用)"

            # 更新计数和内存使用
            self.gpu_module_count[selected_gpu] += 1
            memory_increment = 0.15 if module_info.memory_requirement == 'high' else 0.08  # 更激进的内存估计
            self.gpu_memory_usage[selected_gpu] += memory_increment

            # 记录分配
            if selected_gpu == self.primary_gpu:
                self.gpu0_modules.add(module_info.name)
            else:
                self.gpu1_modules.add(module_info.name)

            print(f"🎯 {module_info.name} → GPU{selected_gpu} ({allocation_reason}) [负载: GPU0={gpu0_count+1 if selected_gpu==self.primary_gpu else gpu0_count}, GPU1={gpu1_count+1 if selected_gpu==self.secondary_gpu else gpu1_count}]")

            return selected_gpu
    
    def release_gpu(self, gpu_id: int, module_info: ModuleInfo):
        """释放GPU资源"""
        with self.gpu_lock:
            if gpu_id in self.gpu_memory_usage:
                # 更新计数和内存使用
                self.gpu_module_count[gpu_id] = max(0, self.gpu_module_count[gpu_id] - 1)
                memory_decrement = 0.15 if module_info.memory_requirement == 'high' else 0.08
                self.gpu_memory_usage[gpu_id] = max(0.0, self.gpu_memory_usage[gpu_id] - memory_decrement)

                # 从模块集合中移除
                self.gpu0_modules.discard(module_info.name)
                self.gpu1_modules.discard(module_info.name)

                print(f"🔄 释放GPU{gpu_id}资源 ({module_info.name}) [剩余负载: GPU0={self.gpu_module_count.get(self.primary_gpu, 0)}, GPU1={self.gpu_module_count.get(self.secondary_gpu, 0)}]")
    
    def get_gpu_status(self) -> Dict[str, Any]:
        """获取GPU状态"""
        return {
            "gpu_memory_usage": self.gpu_memory_usage.copy(),
            "gpu_module_count": self.gpu_module_count.copy(),
            "gpu0_modules": list(self.gpu0_modules),
            "gpu1_modules": list(self.gpu1_modules),
            "total_modules": len(self.gpu0_modules) + len(self.gpu1_modules),
            "gpu_capabilities": self.gpu_capabilities.copy(),
            "utilization_rate": {
                f"GPU{self.primary_gpu}": f"{len(self.gpu0_modules)}/{self.config.gpu0_max_modules} ({self.gpu_memory_usage.get(self.primary_gpu, 0)*100:.1f}%)",
                f"GPU{self.secondary_gpu}": f"{len(self.gpu1_modules)}/{self.config.gpu1_max_modules} ({self.gpu_memory_usage.get(self.secondary_gpu, 0)*100:.1f}%)"
            }
        }

    def print_gpu_utilization(self):
        """打印GPU利用率"""
        print(f"\n🔧 GPU利用率统计:")
        print(f"   GPU{self.primary_gpu} (主): {len(self.gpu0_modules)} 个模块 ({self.gpu_memory_usage.get(self.primary_gpu, 0)*100:.1f}% 显存)")
        print(f"   GPU{self.secondary_gpu} (备): {len(self.gpu1_modules)} 个模块 ({self.gpu_memory_usage.get(self.secondary_gpu, 0)*100:.1f}% 显存)")
        print(f"   总模块数: {len(self.gpu0_modules) + len(self.gpu1_modules)}")

        if self.gpu0_modules:
            print(f"   GPU{self.primary_gpu}模块: {', '.join(sorted(self.gpu0_modules))}")
        if self.gpu1_modules:
            print(f"   GPU{self.secondary_gpu}模块: {', '.join(sorted(self.gpu1_modules))}")


class DetectionModuleWrapper:
    """检测模块统一包装器"""
    
    def __init__(self, module_info: ModuleInfo, gpu_manager: GPUResourceManager):
        self.module_info = module_info
        self.gpu_manager = gpu_manager
        self.module = None
        self.gpu_id = -1
        self.is_loaded = False
    
    def load_module(self) -> bool:
        """加载检测模块"""
        try:
            print(f"🔄 加载模块: {self.module_info.name} ({self.module_info.category})")
            
            # 分配GPU
            self.gpu_id = self.gpu_manager.allocate_gpu(self.module_info)
            
            # 动态导入模块
            module_path = self.module_info.module_path
            if os.path.exists(module_path):
                # 添加模块目录到路径
                module_dir = os.path.dirname(module_path)
                if module_dir not in sys.path:
                    sys.path.insert(0, module_dir)
                
                # 导入模块
                module_name = os.path.basename(module_path).replace('.py', '')
                self.module = __import__(module_name)
                
                print(f"✅ 成功加载模块: {self.module_info.name}")
                self.is_loaded = True
                return True
            else:
                print(f"❌ 模块文件不存在: {module_path}")
                return False
                
        except Exception as e:
            print(f"❌ 加载模块失败 {self.module_info.name}: {e}")
            return False
    
    def process_images(self, image_paths: List[str], output_dir: str,
                      threshold: float = 0.5, enable_batch: bool = True) -> Dict[str, Any]:
        """处理图像列表 - 高性能版本"""
        if not self.is_loaded:
            return {"success": False, "error": "模块未加载"}

        try:
            print(f"� {self.module_info.name} 开始高性能处理 {len(image_paths)} 张图像 (GPU{self.gpu_id})")
            start_time = time.time()

            # 创建模块专用输出目录
            module_output_dir = os.path.join(output_dir, self.module_info.name)
            os.makedirs(module_output_dir, exist_ok=True)

            # 设置GPU环境变量以确保使用正确的GPU
            if self.gpu_id != -1:
                os.environ['CUDA_VISIBLE_DEVICES'] = str(self.gpu_id)
                print(f"🎯 设置CUDA_VISIBLE_DEVICES={self.gpu_id} for {self.module_info.name}")

            success_count = 0

            # 批处理策略
            if enable_batch and hasattr(self.module, 'run_det_gpu_final'):
                # 使用GPU处理函数 - 批处理模式
                try:
                    success_count = self.module.run_det_gpu_final(
                        input_image_dir=os.path.dirname(image_paths[0]),
                        output_json_dir=module_output_dir,
                        threshold=threshold,
                        label_name=self.module_info.label_name
                    )
                except Exception as e:
                    print(f"⚠️  批处理失败，尝试逐个处理: {e}")
                    success_count = self._process_images_individually(image_paths, module_output_dir, threshold)

            elif hasattr(self.module, 'main'):
                # 使用main函数 - 需要修改工作目录
                success_count = self._process_with_main_function(image_paths)

            else:
                print(f"❌ 模块 {self.module_info.name} 没有可用的处理函数")
                return {"success": False, "error": "无可用处理函数"}

            processing_time = time.time() - start_time

            # 检查输出文件并复制到目标目录
            actual_output_dir = self._find_and_copy_outputs(module_output_dir)

            # 计算处理速度
            speed = len(image_paths) / processing_time if processing_time > 0 else 0

            print(f"✅ {self.module_info.name} 高性能处理完成: {success_count}/{len(image_paths)} 张图像, 耗时: {processing_time:.2f}秒, 速度: {speed:.2f} 张/秒")

            return {
                "success": True,
                "processed_count": success_count,
                "total_count": len(image_paths),
                "processing_time": processing_time,
                "processing_speed": speed,
                "output_dir": actual_output_dir,
                "gpu_id": self.gpu_id
            }

        except Exception as e:
            print(f"❌ {self.module_info.name} 处理失败: {e}")
            return {"success": False, "error": str(e)}
        finally:
            # 清理环境变量
            if 'CUDA_VISIBLE_DEVICES' in os.environ:
                del os.environ['CUDA_VISIBLE_DEVICES']

    def _process_images_individually(self, image_paths: List[str], output_dir: str, threshold: float) -> int:
        """逐个处理图像（备用方案）"""
        success_count = 0
        print(f"🔄 逐个处理模式: {len(image_paths)} 张图像, 输出到 {output_dir}, 阈值: {threshold}")

        for image_path in image_paths:
            try:
                # 这里可以添加单张图像处理逻辑
                # 目前简化为假设成功
                success_count += 1
            except Exception as e:
                print(f"⚠️  处理单张图像失败 {image_path}: {e}")
        return success_count

    def _process_with_main_function(self, image_paths: List[str]) -> int:
        """使用main函数处理"""
        original_cwd = os.getcwd()
        try:
            # 切换到模块目录
            module_dir = os.path.dirname(self.module_info.module_path)
            os.chdir(module_dir)
            self.module.main()
            return len(image_paths)  # 假设全部成功
        finally:
            # 恢复原工作目录
            os.chdir(original_cwd)
    
    def _find_and_copy_outputs(self, target_dir: str) -> str:
        """查找模块输出文件并复制到目标目录"""
        try:
            # 模块可能的输出目录
            module_dir = os.path.dirname(self.module_info.module_path)
            possible_output_dirs = [
                os.path.join(module_dir, "test_output_gpu_final"),
                os.path.join(module_dir, "test_output_gpu_pipeline_v2"),
                os.path.join(module_dir, "test_output"),
                os.path.join(module_dir, "output")
            ]
            
            copied_files = 0
            for output_dir in possible_output_dirs:
                if os.path.exists(output_dir):
                    # 复制JSON文件
                    for file in os.listdir(output_dir):
                        if file.endswith('.json'):
                            src_path = os.path.join(output_dir, file)
                            dst_path = os.path.join(target_dir, file)
                            
                            # 复制文件
                            import shutil
                            shutil.copy2(src_path, dst_path)
                            copied_files += 1
                            print(f"📋 复制输出文件: {file}")
            
            if copied_files > 0:
                print(f"✅ 成功复制 {copied_files} 个输出文件到 {target_dir}")
            else:
                print(f"⚠️  未找到输出文件，检查目录: {possible_output_dirs}")
            
            return target_dir
            
        except Exception as e:
            print(f"❌ 复制输出文件失败: {e}")
            return target_dir
    
    def unload_module(self):
        """卸载模块并释放资源"""
        if self.gpu_id != -1:
            self.gpu_manager.release_gpu(self.gpu_id, self.module_info)
        self.is_loaded = False
        print(f"🔄 卸载模块: {self.module_info.name}")


def get_all_detection_modules() -> List[ModuleInfo]:
    """获取所有检测模块信息"""
    base_path = "/home/<USER>/xm/code/coderebuild"
    
    modules = []
    
    # 形态检测模块 (GPU1优先)
    morphology_modules = [
        ModuleInfo(
            name="zhumaidakai",
            module_path=f"{base_path}/seg_det_zhumaidakai/deploy_seg_det_zhumaidakai_onnx_gpu_final.py",
            label_name="zhumaidakai",
            memory_requirement="medium",
            gpu_preference=1,
            color=(0, 0, 255),  # 红色
            category="morphology",
            description="主脉打开检测"
        ),
        ModuleInfo(
            name="zhumaizoushi", 
            module_path=f"{base_path}/seg_det_zhumaizoushi/deploy_seg_det_zhumaizoushi_onnx_gpu_final.py",
            label_name="zhumaizoushi",
            memory_requirement="medium",
            gpu_preference=1,
            color=(255, 0, 0),  # 蓝色
            category="morphology",
            description="主脉走势检测"
        ),
        ModuleInfo(
            name="zheheng",
            module_path=f"{base_path}/seg_det_zheheng/deploy_seg_det_zheheng_onnx_gpu_final.py", 
            label_name="zheheng",
            memory_requirement="medium",
            gpu_preference=1,
            color=(0, 255, 0),  # 绿色
            category="morphology",
            description="折痕检测"
        ),
        ModuleInfo(
            name="zhimai",
            module_path=f"{base_path}/seg_det_zhimai/deploy_seg_det_zhimai_onnx_gpu_final.py",
            label_name="zhimai", 
            memory_requirement="medium",
            gpu_preference=1,
            color=(0, 255, 255),  # 黄色
            category="morphology",
            description="支脉检测"
        ),
        ModuleInfo(
            name="zhimaiqing",
            module_path=f"{base_path}/seg_det_zhimaiqing/deploy_seg_det_zhimaiqing_onnx_gpu_final.py",
            label_name="zhimaiqing",
            memory_requirement="medium", 
            gpu_preference=1,
            color=(255, 255, 0),  # 青色
            category="morphology",
            description="支脉青检测"
        ),
        ModuleInfo(
            name="lunkuo_canque_fill",
            module_path=f"{base_path}/seg_det_lunkuo_canque_fill/deploy_seg_lunkuo_canque_fill_onnx_gpu_final.py",
            label_name="lunkuo_canque_fill",
            memory_requirement="high",  # 显存占用大
            gpu_preference=1,  # 优先GPU1
            color=(128, 0, 128),  # 紫色
            category="morphology",
            description="轮廓残缺补全"
        )
    ]
    
    modules.extend(morphology_modules)

    # 杂色检测模块 (GPU0优先)
    discoloration_modules = [
        ModuleInfo(
            name="condition",
            module_path=f"{base_path}/seg_det_jiaodian/deploy_seg_det_condition_onnx_gpu_final.py",
            label_name="condition",
            memory_requirement="medium",
            gpu_preference=0,
            color=(255, 128, 0),  # 橙色
            category="discoloration",
            description="焦点浮青等检测"
        ),
        ModuleInfo(
            name="hengwen",
            module_path=f"{base_path}/seg_det_hengwen/deploy_seg_det_hengwen_onnx_gpu_final.py",
            label_name="hengwen",
            memory_requirement="medium",
            gpu_preference=0,
            color=(128, 255, 0),  # 黄绿色
            category="discoloration",
            description="横纹检测"
        ),
        ModuleInfo(
            name="kaohong",
            module_path=f"{base_path}/seg_det_kaohong/deploy_seg_det_kaohong_onnx_gpu_final.py",
            label_name="kaohong",
            memory_requirement="medium",
            gpu_preference=0,
            color=(255, 0, 128),  # 粉红色
            category="discoloration",
            description="烤红检测"
        ),
        ModuleInfo(
            name="guahui",
            module_path=f"{base_path}/seg_det_kaohong/deploy_seg_det_guahui_onnx_gpu_final.py",
            label_name="guahui",
            memory_requirement="medium",
            gpu_preference=0,
            color=(128, 128, 255),  # 浅蓝色
            category="discoloration",
            description="挂灰检测"
        ),
        ModuleInfo(
            name="zhousuo",
            module_path=f"{base_path}/seg_det_zhousuo/deploy_seg_det_zhousuo_onnx_gpu_final.py",
            label_name="zhousuo",
            memory_requirement="medium",
            gpu_preference=0,
            color=(255, 128, 128),  # 浅红色
            category="discoloration",
            description="皱缩检测"
        ),
        ModuleInfo(
            name="chaohong",
            module_path=f"{base_path}/seg_det_chaohong/deploy_seg_det_chaohong_onnx_gpu_final.py",
            label_name="chaohong",
            memory_requirement="medium",
            gpu_preference=0,
            color=(128, 255, 255),  # 浅青色
            category="discoloration",
            description="潮红检测"
        )
    ]

    modules.extend(discoloration_modules)

    print(f"🔧 配置完成: {len(morphology_modules)} 个形态检测模块 + {len(discoloration_modules)} 个杂色检测模块")

    return modules


class PipelineScheduler:
    """流水线并行调度器"""

    def __init__(self, config: ComprehensiveDetectionConfig):
        self.config = config
        self.gpu_manager = GPUResourceManager(config.gpu_primary, config.gpu_secondary)

    def run_parallel_detection(self, modules: List[ModuleInfo], image_paths: List[str]) -> Dict[str, Any]:
        """并行运行所有检测模块 - 高性能版本"""
        print(f"\n🚀 双GPU高性能并行检测处理")
        print("="*70)
        print(f"🔧 使用 {len(modules)} 个模块并行处理 {len(image_paths)} 张图像")
        print(f"⚡ 最大工作线程: {self.config.max_workers}")
        print(f"🎯 GPU配置: GPU{self.config.gpu_primary}(主,最多{self.config.gpu0_max_modules}模块) + GPU{self.config.gpu_secondary}(备,最多{self.config.gpu1_max_modules}模块)")

        # 按类别分组显示
        morphology_count = len([m for m in modules if m.category == "morphology"])
        discoloration_count = len([m for m in modules if m.category == "discoloration"])
        print(f"📊 形态检测模块: {morphology_count} 个")
        print(f"📊 杂色检测模块: {discoloration_count} 个")

        # 显示初始GPU状态
        self.gpu_manager.print_gpu_utilization()

        start_time = time.time()
        results = {}

        # 使用线程池并行处理 - 增加并发度
        with ThreadPoolExecutor(max_workers=self.config.max_workers) as executor:
            print(f"\n🚀 启动 {self.config.max_workers} 个并行工作线程...")

            # 提交所有模块任务
            future_to_module = {}
            for module_info in modules:
                future = executor.submit(self._process_module_high_performance, module_info, image_paths)
                future_to_module[future] = module_info

            # 收集结果并实时监控
            completed_count = 0
            for future in as_completed(future_to_module):
                module_info = future_to_module[future]
                completed_count += 1

                try:
                    result = future.result(timeout=600)  # 增加到10分钟超时
                    results[module_info.name] = result

                    if result["success"]:
                        speed = result.get('processing_speed', 0)
                        gpu_id = result.get('gpu_id', -1)
                        print(f"✅ [{completed_count:2d}/{len(modules)}] {module_info.name} ({module_info.category}) GPU{gpu_id} 完成: {result['processed_count']} 张图像, 耗时: {result['processing_time']:.2f}秒, 速度: {speed:.2f} 张/秒")
                    else:
                        print(f"❌ [{completed_count:2d}/{len(modules)}] {module_info.name} ({module_info.category}) 失败: {result.get('error', '未知错误')}")

                    # 每完成几个模块就显示一次GPU状态
                    if completed_count % 3 == 0:
                        self.gpu_manager.print_gpu_utilization()

                except Exception as e:
                    print(f"❌ [{completed_count:2d}/{len(modules)}] {module_info.name} ({module_info.category}) 异常: {e}")
                    results[module_info.name] = {"success": False, "error": str(e)}

        total_time = time.time() - start_time

        # 统计结果
        successful_modules = [name for name, result in results.items() if result["success"]]
        failed_modules = [name for name, result in results.items() if not result["success"]]

        # 按类别统计
        successful_morphology = [name for name in successful_modules
                               if any(m.name == name and m.category == "morphology" for m in modules)]
        successful_discoloration = [name for name in successful_modules
                                  if any(m.name == name and m.category == "discoloration" for m in modules)]

        print(f"\n📊 并行处理结果:")
        print(f"✅ 总成功: {len(successful_modules)}/{len(modules)} 个模块")
        print(f"   形态检测: {len(successful_morphology)} 个")
        print(f"   杂色检测: {len(successful_discoloration)} 个")
        print(f"❌ 失败: {len(failed_modules)} 个模块")
        print(f"⏱️  总耗时: {total_time:.2f}秒")

        if failed_modules:
            print("失败的模块:")
            for module_name in failed_modules:
                error_msg = results[module_name].get('error', '未知错误')
                print(f"   - {module_name}: {error_msg}")

        # 显示GPU使用情况
        gpu_status = self.gpu_manager.get_gpu_status()
        print(f"\n🔧 GPU使用统计:")
        print(f"   GPU{self.config.gpu_primary} (主): {len(gpu_status['gpu0_modules'])} 个模块")
        print(f"   GPU{self.config.gpu_secondary} (备): {len(gpu_status['gpu1_modules'])} 个模块")

        return {
            "success": len(successful_modules) > 0,
            "total_time": total_time,
            "successful_modules": successful_modules,
            "failed_modules": failed_modules,
            "results": results,
            "gpu_status": gpu_status
        }

    def _process_module(self, module_info: ModuleInfo, image_paths: List[str]) -> Dict[str, Any]:
        """处理单个模块"""
        wrapper = DetectionModuleWrapper(module_info, self.gpu_manager)

        try:
            # 加载模块
            if not wrapper.load_module():
                return {"success": False, "error": "模块加载失败"}

            # 处理图像
            result = wrapper.process_images(image_paths, self.config.output_dir, self.config.threshold)
            return result

        finally:
            # 清理资源
            wrapper.unload_module()

    def _process_module_high_performance(self, module_info: ModuleInfo, image_paths: List[str]) -> Dict[str, Any]:
        """高性能处理单个模块"""
        wrapper = DetectionModuleWrapper(module_info, self.gpu_manager)

        try:
            # 加载模块
            if not wrapper.load_module():
                return {"success": False, "error": "模块加载失败"}

            # 高性能处理图像
            result = wrapper.process_images(
                image_paths,
                self.config.output_dir,
                self.config.threshold,
                enable_batch=self.config.enable_batch_processing
            )

            # 添加性能指标
            if result["success"]:
                result["performance_metrics"] = {
                    "gpu_id": wrapper.gpu_id,
                    "memory_requirement": module_info.memory_requirement,
                    "category": module_info.category,
                    "batch_processing": self.config.enable_batch_processing
                }

            return result

        finally:
            # 清理资源
            wrapper.unload_module()

    def merge_detection_results(self, image_paths: List[str], successful_modules: List[str],
                              all_modules: List[ModuleInfo]) -> bool:
        """合并检测结果"""
        print(f"\n🔄 合并检测结果")
        print("="*50)

        try:
            for image_path in image_paths:
                image_name = os.path.basename(image_path)
                base_name = os.path.splitext(image_name)[0]

                # 创建合并的JSON结构
                merged_result = {
                    "version": "4.5.6",
                    "flags": {},
                    "shapes": [],
                    "imagePath": image_name,
                    "imageData": None,
                    "imageHeight": 0,
                    "imageWidth": 0,
                    "detection_info": {
                        "modules": successful_modules,
                        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                        "total_detections": 0,
                        "morphology_detections": 0,
                        "discoloration_detections": 0,
                        "module_categories": {}
                    }
                }

                total_detections = 0
                morphology_detections = 0
                discoloration_detections = 0

                # 合并各模块的检测结果
                for module_name in successful_modules:
                    module_json_path = os.path.join(
                        self.config.output_dir, module_name, f"{base_name}.json"
                    )

                    # 获取模块信息
                    module_info = next((m for m in all_modules if m.name == module_name), None)

                    if os.path.exists(module_json_path):
                        try:
                            with open(module_json_path, 'r', encoding='utf-8') as f:
                                module_data = json.load(f)

                            # 添加模块的检测结果
                            if "shapes" in module_data:
                                module_detection_count = 0
                                for shape in module_data["shapes"]:
                                    # 添加模块信息到shape
                                    shape["module"] = module_name
                                    if module_info:
                                        shape["category"] = module_info.category
                                        shape["description"] = module_info.description
                                    merged_result["shapes"].append(shape)
                                    total_detections += 1
                                    module_detection_count += 1

                                    # 按类别统计
                                    if module_info and module_info.category == "morphology":
                                        morphology_detections += 1
                                    elif module_info and module_info.category == "discoloration":
                                        discoloration_detections += 1

                                # 记录各模块的检测数量
                                merged_result["detection_info"]["module_categories"][module_name] = {
                                    "count": module_detection_count,
                                    "category": module_info.category if module_info else "unknown"
                                }

                            # 更新图像尺寸信息
                            if "imageHeight" in module_data and module_data["imageHeight"] > 0:
                                merged_result["imageHeight"] = module_data["imageHeight"]
                                merged_result["imageWidth"] = module_data["imageWidth"]

                        except Exception as e:
                            print(f"⚠️  读取模块结果失败 {module_name}/{base_name}.json: {e}")
                    else:
                        print(f"⚠️  模块结果文件不存在: {module_json_path}")

                # 更新统计信息
                merged_result["detection_info"]["total_detections"] = total_detections
                merged_result["detection_info"]["morphology_detections"] = morphology_detections
                merged_result["detection_info"]["discoloration_detections"] = discoloration_detections

                # 保存合并结果
                merged_json_path = os.path.join(self.config.output_dir, f"{base_name}_comprehensive.json")
                with open(merged_json_path, 'w', encoding='utf-8') as f:
                    json.dump(merged_result, f, ensure_ascii=False, indent=2)

                print(f"✅ 合并完成: {base_name}_comprehensive.json")
                print(f"   总检测: {total_detections} 个 (形态: {morphology_detections}, 杂色: {discoloration_detections})")

            print(f"🎉 所有检测结果合并完成！")
            return True

        except Exception as e:
            print(f"❌ 合并检测结果失败: {e}")
            return False


def test_single_module(module_info: ModuleInfo, config: ComprehensiveDetectionConfig) -> bool:
    """测试单个模块是否能正常生成JSON"""
    print(f"\n🧪 测试模块: {module_info.name} ({module_info.category})")
    print("-" * 40)

    try:
        # 创建GPU管理器
        gpu_manager = GPUResourceManager(config.gpu_primary, config.gpu_secondary)

        # 创建模块包装器
        wrapper = DetectionModuleWrapper(module_info, gpu_manager)

        # 加载模块
        if not wrapper.load_module():
            print(f"❌ 模块 {module_info.name} 加载失败")
            return False

        # 获取测试图像
        image_files = [f for f in os.listdir(config.input_dir) if f.endswith('.bmp')]
        if not image_files:
            print(f"❌ 测试目录中没有找到图像文件")
            return False

        # 只测试第一张图像
        test_image = os.path.join(config.input_dir, image_files[0])

        # 处理图像
        result = wrapper.process_images([test_image], config.output_dir, config.threshold)

        # 检查结果
        if result["success"]:
            # 检查是否生成了JSON文件
            module_output_dir = result["output_dir"]
            json_files = [f for f in os.listdir(module_output_dir) if f.endswith('.json')]

            if json_files:
                print(f"✅ 模块 {module_info.name} 测试成功，生成了 {len(json_files)} 个JSON文件")

                # 验证JSON格式
                test_json_path = os.path.join(module_output_dir, json_files[0])
                try:
                    with open(test_json_path, 'r', encoding='utf-8') as f:
                        json_data = json.load(f)
                    print(f"✅ JSON格式验证通过 (包含 {len(json_data.get('shapes', []))} 个检测结果)")
                    return True
                except Exception as e:
                    print(f"❌ JSON格式验证失败: {e}")
                    return False
            else:
                print(f"❌ 模块 {module_info.name} 没有生成JSON文件")
                return False
        else:
            print(f"❌ 模块 {module_info.name} 处理失败: {result.get('error', '未知错误')}")
            return False

    except Exception as e:
        print(f"❌ 测试模块 {module_info.name} 时发生异常: {e}")
        return False
    finally:
        # 清理资源
        if 'wrapper' in locals():
            wrapper.unload_module()


def main():
    """主函数"""
    print("🔥🔥🔥 烟叶综合检测系统 - 双GPU流水线并行版本 🔥🔥🔥")
    print("="*80)
    print("集成12个检测模块：6个形态检测 + 6个杂色检测")
    print("="*80)

    # 创建配置
    config = ComprehensiveDetectionConfig()

    # 验证输入输出目录
    if not os.path.exists(config.input_dir):
        print(f"❌ 输入目录不存在: {config.input_dir}")
        return False

    os.makedirs(config.output_dir, exist_ok=True)
    print(f"📁 输入目录: {config.input_dir}")
    print(f"📁 输出目录: {config.output_dir}")

    # 获取所有检测模块
    modules = get_all_detection_modules()
    print(f"🔧 检测到 {len(modules)} 个检测模块")

    # 获取图像文件列表
    image_files = [f for f in os.listdir(config.input_dir) if f.endswith('.bmp')]
    image_paths = [os.path.join(config.input_dir, f) for f in image_files]
    print(f"📷 找到 {len(image_paths)} 张图像")

    if not image_paths:
        print("❌ 没有找到图像文件")
        return False

    # 第一阶段：测试每个模块
    print(f"\n🧪 第一阶段：测试各个模块")
    print("="*60)

    successful_modules = []
    failed_modules = []

    for module_info in modules:
        if test_single_module(module_info, config):
            successful_modules.append(module_info)
        else:
            failed_modules.append(module_info)

    # 报告测试结果
    print(f"\n📊 模块测试结果:")
    print(f"✅ 成功: {len(successful_modules)} 个模块")
    print(f"❌ 失败: {len(failed_modules)} 个模块")

    # 按类别统计
    successful_morphology = [m for m in successful_modules if m.category == "morphology"]
    successful_discoloration = [m for m in successful_modules if m.category == "discoloration"]

    print(f"   形态检测: {len(successful_morphology)} 个")
    print(f"   杂色检测: {len(successful_discoloration)} 个")

    if failed_modules:
        print("失败的模块:")
        for module in failed_modules:
            print(f"   - {module.name} ({module.category})")

    if not successful_modules:
        print("❌ 没有可用的检测模块，程序退出")
        return False

    print(f"\n✅ 第一阶段测试完成，{len(successful_modules)} 个模块可用")

    # 第二阶段：高性能并行处理
    print(f"\n🚀 第二阶段：高性能并行处理")
    print("="*60)

    scheduler = PipelineScheduler(config)

    # 显示配置信息
    print(f"⚡ 高性能配置:")
    print(f"   激进GPU使用: {config.enable_aggressive_gpu_usage}")
    print(f"   批处理模式: {config.enable_batch_processing}")
    print(f"   最大工作线程: {config.max_workers}")
    print(f"   GPU0最大模块: {config.gpu0_max_modules}")
    print(f"   GPU1最大模块: {config.gpu1_max_modules}")

    parallel_result = scheduler.run_parallel_detection(successful_modules, image_paths)

    if not parallel_result["success"]:
        print("❌ 并行处理失败")
        return False

    # 第三阶段：合并结果
    merge_success = scheduler.merge_detection_results(
        image_paths, parallel_result["successful_modules"], successful_modules
    )

    if not merge_success:
        print("❌ 结果合并失败")
        return False

    print(f"\n🎉 烟叶综合检测系统处理完成！")
    print(f"📊 处理统计:")
    print(f"   图像数量: {len(image_paths)}")
    print(f"   成功模块: {len(parallel_result['successful_modules'])}")
    print(f"   总耗时: {parallel_result['total_time']:.2f}秒")
    print(f"   平均每张图像: {parallel_result['total_time'] / len(image_paths):.2f}秒")
    print(f"   输出目录: {config.output_dir}")

    # GPU使用统计
    gpu_status = parallel_result["gpu_status"]
    print(f"\n🔧 最终GPU使用统计:")
    print(f"   GPU{config.gpu_primary} (主): {len(gpu_status['gpu0_modules'])} 个模块")
    print(f"   GPU{config.gpu_secondary} (备): {len(gpu_status['gpu1_modules'])} 个模块")

    # 显示详细的性能指标
    total_speed = 0
    gpu0_speed = 0
    gpu1_speed = 0

    for module_name, result in parallel_result["results"].items():
        if result.get("success") and "processing_speed" in result:
            speed = result["processing_speed"]
            total_speed += speed

            gpu_id = result.get("gpu_id", -1)
            if gpu_id == config.gpu_primary:
                gpu0_speed += speed
            elif gpu_id == config.gpu_secondary:
                gpu1_speed += speed

    print(f"\n⚡ 性能统计:")
    print(f"   总处理速度: {total_speed:.2f} 张/秒")
    print(f"   GPU{config.gpu_primary}速度: {gpu0_speed:.2f} 张/秒")
    print(f"   GPU{config.gpu_secondary}速度: {gpu1_speed:.2f} 张/秒")
    print(f"   GPU利用率: GPU{config.gpu_primary}={gpu0_speed/total_speed*100:.1f}%, GPU{config.gpu_secondary}={gpu1_speed/total_speed*100:.1f}%")

    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
